﻿using System.ComponentModel.DataAnnotations;
namespace SCanProduct.Model.Dto;
public class CreateScanProductV2Dto
{
    [StringLength(100)] public string ItemCode { get; set; } = null!;
    [StringLength(100)] public string WarehouseId { get; set; } = null!;
    [StringLength(100)] public string LocationCode { get; set; } = null!;
    [StringLength(100)] public string BarCode { get; set; } = null!;
    [StringLength(100)] public string UnitOfMeasure { get; set; } = null!;
    [StringLength(50)] public string? CreateBy { get; set; }
}
