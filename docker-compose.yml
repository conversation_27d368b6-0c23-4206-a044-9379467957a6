version: '3.4'

services:
    scanproduct.api:
        image: ${DOCKER_REGISTRY-}scanproductapi
        restart: always
        build:
            context: .
            dockerfile: SCanProduct.API/Dockerfile
        ports:
            - "2820:8080"

    scanproduct.ui:
        image: ${DOCKER_REGISTRY-}scanproductui
        restart: always
        build:
            context: .
            dockerfile: SCanProduct.UI/Dockerfile
        ports:
            - "2821:8080"