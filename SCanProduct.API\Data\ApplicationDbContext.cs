﻿using Microsoft.EntityFrameworkCore;
using Image=SCanProduct.Model.Model.Image;
using Item=SCanProduct.Model.Model.Item;
using ItemUnitOfMeasure=SCanProduct.Model.Model.ItemUnitOfMeasure;
using ScanProduct=SCanProduct.Model.Model.ScanProduct;
using ScanProductV2=SCanProduct.Model.Model.ScanProductV2;
using UnitOfMeasure=SCanProduct.Model.Model.UnitOfMeasure;
using Warehouse=SCanProduct.Model.Model.Warehouse;
namespace SCanProduct.API.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Image> Images { get; set; }

    public virtual DbSet<Item> Items { get; set; }

    public virtual DbSet<ItemUnitOfMeasure> ItemUnitOfMeasures { get; set; }

    public virtual DbSet<ScanProduct> ScanProducts { get; set; }

    public virtual DbSet<UnitOfMeasure> UnitOfMeasures { get; set; }

    public virtual DbSet<ScanProductV2> ScanProductV2S { get; set; }

    public virtual DbSet<Warehouse> Warehouses { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Image>(entity =>
        {
            entity.HasKey(e => e.ImageId).HasName("PK_ImageProduct");

            entity.Property(e => e.Name).HasMaxLength(500);
            entity.Property(e => e.Size).HasMaxLength(50);
            entity.Property(e => e.Type).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(500);

            entity.HasOne(d => d.Product).WithMany(p => p.Images)
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_Images_SCanProducts");
        });

        modelBuilder.Entity<Item>(entity =>
        {
            entity.HasKey(e => e.No);

            entity.ToTable("Item");

            entity.Property(e => e.No)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("No_");
            entity.Property(e => e.AccountType).HasColumnName("Account Type");
            entity.Property(e => e.Aging).HasColumnType("decimal(18, 10)");
            entity.Property(e => e.AllocatedCost)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Allocated Cost");
            entity.Property(e => e.AllowCampaign).HasColumnName("Allow_Campaign");
            entity.Property(e => e.AllowInvoiceDisc).HasColumnName("Allow Invoice Disc_");
            entity.Property(e => e.AllowLineDisc).HasColumnName("Allow Line Disc_");
            entity.Property(e => e.AllowWallet).HasColumnName("Allow_Wallet");
            entity.Property(e => e.AlternativeItemNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Alternative Item No_");
            entity.Property(e => e.BaseUnitOfMeasure)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Base Unit of Measure");
            entity.Property(e => e.BinCode)
                .HasMaxLength(500)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Bin Code");
            entity.Property(e => e.BomVersionCode).HasColumnName("BOM Version Code");
            entity.Property(e => e.BoxUnitOfMeasure)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Box Unit of Measure");
            entity.Property(e => e.BudgetProfit)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Budget Profit");
            entity.Property(e => e.BudgetQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Budget Quantity");
            entity.Property(e => e.BudgetedAmount)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Budgeted Amount");
            entity.Property(e => e.CategoryGroupNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CategoryNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CategoryTypeNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CharacterGroup)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Character Group");
            entity.Property(e => e.Checkjson).HasColumnName("checkjson");
            entity.Property(e => e.CifCurrencyCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("CIF Currency Code");
            entity.Property(e => e.CifPrice)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("CIF Price");
            entity.Property(e => e.Class)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CommissionGroup)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Commission Group");
            entity.Property(e => e.CommonItemNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Common Item No_");
            entity.Property(e => e.CompanyRegistration)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Company Registration");
            entity.Property(e => e.Contraindications)
                .HasMaxLength(200)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CostingMethod).HasColumnName("Costing Method");
            entity.Property(e => e.CountryOfOriginCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Country of Origin Code");
            entity.Property(e => e.CountryPurchasedCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Country Purchased Code");
            entity.Property(e => e.CustomDate)
                .HasColumnType("datetime")
                .HasColumnName("Custom Date");
            entity.Property(e => e.CustomNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Custom No_");
            entity.Property(e => e.Description)
                .HasMaxLength(2000)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Description2)
                .HasMaxLength(2000)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Description 2");
            entity.Property(e => e.Diameter).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Durability)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.DutyCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Duty Code");
            entity.Property(e => e.DutyDue)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Duty Due %");
            entity.Property(e => e.DutyUnitConversion)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Duty Unit Conversion");
            entity.Property(e => e.EquivalantFinishedQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Equivalant Finished Quantity");
            entity.Property(e => e.ExperimentBom)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Experiment BOM");
            entity.Property(e => e.ExperimentDuration)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Experiment Duration");
            entity.Property(e => e.ExperimentRouting)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Experiment Routing");
            entity.Property(e => e.ExpirationCalculation)
                .HasMaxLength(64)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Expiration Calculation");
            entity.Property(e => e.ExpirationDate).HasColumnName("Expiration Date");
            entity.Property(e => e.FlushingMethod).HasColumnName("Flushing Method");
            entity.Property(e => e.FormOfInvoice)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Form of Invoice");
            entity.Property(e => e.FreightType)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Freight Type");
            entity.Property(e => e.GenProdPostingGroup)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Gen_ Prod_ Posting Group");
            entity.Property(e => e.GiaNhapGanNhat).HasColumnType("decimal(25, 10)");
            entity.Property(e => e.GrossWeight)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Gross Weight");
            entity.Property(e => e.HaravanStatus).HasColumnName("haravan status");
            entity.Property(e => e.Height).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.IdProduct).HasColumnName("ID product");
            entity.Property(e => e.IdVarians).HasColumnName("ID varians");
            entity.Property(e => e.ImportLicenseNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Import License No_");
            entity.Property(e => e.ImportLicenseQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Import License Quantity");
            entity.Property(e => e.Importer)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.IncludeInventory).HasColumnName("Include Inventory");
            entity.Property(e => e.IndirectCost)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Indirect Cost %");
            entity.Property(e => e.IngredientWeight)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Ingredient Weight");
            entity.Property(e => e.InternalAmount)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Internal Amount");
            entity.Property(e => e.InternalAmountLcy)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Internal Amount (LCY)");
            entity.Property(e => e.InventoryPostingGroup)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Inventory Posting Group");
            entity.Property(e => e.InventoryValueZero).HasColumnName("Inventory Value Zero");
            entity.Property(e => e.ItemCategoryAtcCode)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("Item Category ATC Code");
            entity.Property(e => e.ItemCategoryCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Category Code");
            entity.Property(e => e.ItemDiscGroup)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Disc_ Group");
            entity.Property(e => e.ItemGroup1)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 1");
            entity.Property(e => e.ItemGroup10)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 10");
            entity.Property(e => e.ItemGroup11)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 11");
            entity.Property(e => e.ItemGroup12)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 12");
            entity.Property(e => e.ItemGroup13)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 13");
            entity.Property(e => e.ItemGroup14)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 14");
            entity.Property(e => e.ItemGroup15)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 15");
            entity.Property(e => e.ItemGroup16)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 16");
            entity.Property(e => e.ItemGroup17)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 17");
            entity.Property(e => e.ItemGroup18)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 18");
            entity.Property(e => e.ItemGroup19)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 19");
            entity.Property(e => e.ItemGroup2)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 2");
            entity.Property(e => e.ItemGroup20)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 20");
            entity.Property(e => e.ItemGroup3)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 3");
            entity.Property(e => e.ItemGroup4)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 4");
            entity.Property(e => e.ItemGroup5)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 5");
            entity.Property(e => e.ItemGroup6)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("item group 6");
            entity.Property(e => e.ItemGroup7)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 7");
            entity.Property(e => e.ItemGroup8)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 8");
            entity.Property(e => e.ItemGroup9)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Group 9");
            entity.Property(e => e.ItemLocation)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ItemSpecification)
                .HasMaxLength(1000)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item Specification");
            entity.Property(e => e.Label)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.LastCountingPeriodUpdate)
                .HasColumnType("datetime")
                .HasColumnName("Last Counting Period Update");
            entity.Property(e => e.LastDateModified)
                .HasColumnType("datetime")
                .HasColumnName("Last Date Modified");
            entity.Property(e => e.LastDirectCost)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Last Direct Cost");
            entity.Property(e => e.LastUnitCostCalcDate)
                .HasColumnType("datetime")
                .HasColumnName("Last Unit Cost Calc_ Date");
            entity.Property(e => e.LastUserModified)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Last User Modified");
            entity.Property(e => e.LeadTimeCalculation)
                .HasMaxLength(64)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Lead Time Calculation");
            entity.Property(e => e.Length).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.LoginId)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Login ID");
            entity.Property(e => e.LotPickMod).HasColumnName("Lot Pick Mod");
            entity.Property(e => e.LotSize)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Lot Size");
            entity.Property(e => e.LowLevelCode).HasColumnName("Low-Level Code");
            entity.Property(e => e.MainIngredient)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Manager)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ManufacturerCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Manufacturer Code");
            entity.Property(e => e.ManufacturingPolicy).HasColumnName("Manufacturing Policy");
            entity.Property(e => e.MaximumInventory)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Maximum Inventory");
            entity.Property(e => e.MaximumOrderQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Maximum Order Quantity");
            entity.Property(e => e.MinimumInventory)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Minimum Inventory");
            entity.Property(e => e.MinimumOrderQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Minimum Order Quantity");
            entity.Property(e => e.Name)
                .HasMaxLength(160)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Name2)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Name 2");
            entity.Property(e => e.Name3)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Name 3");
            entity.Property(e => e.NetWeight)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Net Weight");
            entity.Property(e => e.NextCountingPeriod)
                .HasMaxLength(500)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Next Counting Period");
            entity.Property(e => e.No2)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("No_ 2");
            entity.Property(e => e.No3)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("No_ 3");
            entity.Property(e => e.No4)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("No_ 4");
            entity.Property(e => e.No5)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("No_ 5");
            entity.Property(e => e.NumberOfInvFrom)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Number of Inv_ From");
            entity.Property(e => e.NumberOfInvTo)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Number of Inv_ To");
            entity.Property(e => e.OfBom)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.OfItem)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.OnHaravan).HasColumnName("on haravan");
            entity.Property(e => e.OpenDateFrom).HasColumnName("Open Date From");
            entity.Property(e => e.OpenDateTo).HasColumnName("Open Date To");
            entity.Property(e => e.OracleSyncStatus).HasColumnName("oracle_sync_status");
            entity.Property(e => e.OrderMultiple)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Order Multiple");
            entity.Property(e => e.OrderTrackingPolicy).HasColumnName("Order Tracking Policy");
            entity.Property(e => e.OverheadRate)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Overhead Rate");
            entity.Property(e => e.ParValue)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Par Value");
            entity.Property(e => e.ParValueOfConv)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Par Value of Conv_");
            entity.Property(e => e.PhysInvtCountingPeriodCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Phys Invt Counting Period Code");
            entity.Property(e => e.PickMethod).HasColumnName("Pick Method");
            entity.Property(e => e.Picture)
                .HasMaxLength(160)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.PictureNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Picture No_");
            entity.Property(e => e.PlaceOfOriginCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Place of Origin Code");
            entity.Property(e => e.PointTe).HasColumnName("Point TE");
            entity.Property(e => e.PostingDate).HasColumnName("Posting Date");
            entity.Property(e => e.PriceProfitCalculation).HasColumnName("Price_Profit Calculation");
            entity.Property(e => e.PriceUnitConversion).HasColumnName("Price Unit Conversion");
            entity.Property(e => e.PrizeStructure)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Prize Structure");
            entity.Property(e => e.ProductGroupCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Product Group Code");
            entity.Property(e => e.ProductionBomNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Production BOM No_");
            entity.Property(e => e.ProductionStandard)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Production Standard");
            entity.Property(e => e.ProductionWorkflowVersion).HasColumnName("Production Workflow Version");
            entity.Property(e => e.Profit)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Profit %");
            entity.Property(e => e.PurchUnitOfMeasure)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Purch_ Unit of Measure");
            entity.Property(e => e.QcTest).HasColumnName("QC Test");
            entity.Property(e => e.Qtqc)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("QTQC");
            entity.Property(e => e.QualityMeasureCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Quality Measure Code");
            entity.Property(e => e.QuotaAddedNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Quota Added No_");
            entity.Property(e => e.QuotaAddedQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Quota Added Quantity");
            entity.Property(e => e.QuotaNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Quota No_");
            entity.Property(e => e.QuotaQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Quota Quantity");
            entity.Property(e => e.QuotaRequest).HasColumnName("Quota Request");
            entity.Property(e => e.RegistrationNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ReorderCycle)
                .HasMaxLength(64)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Reorder Cycle");
            entity.Property(e => e.ReorderPoint)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Reorder Point");
            entity.Property(e => e.ReorderQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Reorder Quantity");
            entity.Property(e => e.ReorderingPolicy).HasColumnName("Reordering Policy");
            entity.Property(e => e.ReplenishmentSystem).HasColumnName("Replenishment System");
            entity.Property(e => e.Route)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.RoutingNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Routing No_");
            entity.Property(e => e.RowId)
                .ValueGeneratedOnAdd()
                .HasColumnName("RowID");
            entity.Property(e => e.SafetyLeadTime)
                .HasMaxLength(64)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Safety Lead Time");
            entity.Property(e => e.SafetyStockQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Safety Stock Quantity");
            entity.Property(e => e.SalaryGroup)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Salary Group");
            entity.Property(e => e.SalesUnitOfMeasure)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Sales Unit of Measure");
            entity.Property(e => e.Scrap)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Scrap %");
            entity.Property(e => e.SearchName)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Search Name");
            entity.Property(e => e.SeriInvoice)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Seri Invoice");
            entity.Property(e => e.ShelfNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Shelf No_");
            entity.Property(e => e.ShowNameReport)
                .HasMaxLength(1000)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ShowReport)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.SpecialEquipmentCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Special Equipment Code");
            entity.Property(e => e.Specification)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Specification2)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Specification 2");
            entity.Property(e => e.Specification3)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Specification 3");
            entity.Property(e => e.Specification4)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Specification 4");
            entity.Property(e => e.Specification5)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Specification 5");
            entity.Property(e => e.Specification6)
                .HasMaxLength(1000)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Specification 6");
            entity.Property(e => e.Standard)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.StandardCost)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Standard Cost");
            entity.Property(e => e.StandardPrice)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Standard Price");
            entity.Property(e => e.StatisticsGroup).HasColumnName("Statistics Group");
            entity.Property(e => e.StatusJson).HasColumnName("status json");
            entity.Property(e => e.SubGroupCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Sub Group Code");
            entity.Property(e => e.SubGroupCode1)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Sub Group Code 1");
            entity.Property(e => e.SubGroupCode2)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Sub Group Code 2");
            entity.Property(e => e.Surface).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.SymbolItem)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Symbol Item");
            entity.Property(e => e.SymbolTicket)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Symbol Ticket");
            entity.Property(e => e.TariffNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Tariff No_");
            entity.Property(e => e.TaxGroupCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Tax Group Code");
            entity.Property(e => e.TechnicalDocs)
                .HasMaxLength(200)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Technical Docs");
            entity.Property(e => e.TechnicalStandards)
                .HasMaxLength(200)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Technical Standards");
            entity.Property(e => e.Temp).HasColumnName("temp");
            entity.Property(e => e.Thick).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Totaling)
                .HasMaxLength(160)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.UnitCost)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit Cost");
            entity.Property(e => e.UnitCostVat)
                .HasColumnType("decimal(18, 0)")
                .HasColumnName("Unit Cost (VAT)");
            entity.Property(e => e.UnitListPrice)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit List Price");
            entity.Property(e => e.UnitListPriceCurrencyCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Unit List Price Currency Code");
            entity.Property(e => e.UnitListPriceIncludedVat)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit List Price Included VAT");
            entity.Property(e => e.UnitListPriceIncludedVatCurrencyCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Unit List Price Included VAT Currency Code");
            entity.Property(e => e.UnitPrice)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit Price");
            entity.Property(e => e.UnitPrice1)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit Price 1");
            entity.Property(e => e.UnitPriceRegis).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.UnitVolume)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Unit Volume");
            entity.Property(e => e.UnitsPerParcel)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Units per Parcel");
            entity.Property(e => e.UomCif)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("UOM CIF");
            entity.Property(e => e.UomUnitListPrice)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("UOM Unit List Price");
            entity.Property(e => e.UomUnitListPriceIncludedVat)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("UOM Unit List Price Included VAT");
            entity.Property(e => e.UomWholesalePrice)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("UOM Wholesale Price");
            entity.Property(e => e.UomWholesalePriceIncludedVat)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("UOM Wholesale Price Included VAT");
            entity.Property(e => e.UserControl)
                .HasMaxLength(250)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("User Control");
            entity.Property(e => e.VatProdPostingGroup)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("VAT Prod_ Posting Group");
            entity.Property(e => e.VendorAuthorizationNo)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Vendor Authorization No_");
            entity.Property(e => e.VendorItemNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Vendor Item No_");
            entity.Property(e => e.VendorNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Vendor No_");
            entity.Property(e => e.VersionCode).HasColumnName("Version Code");
            entity.Property(e => e.VersionQc)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Version QC");
            entity.Property(e => e.Visa)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.VisaIssuedDate).HasColumnName("Visa Issued Date");
            entity.Property(e => e.VisaIssuedNo)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Visa Issued No_");
            entity.Property(e => e.WebStatus).HasColumnName("Web Status");
            entity.Property(e => e.Weight).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.WholesalePrice)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Wholesale Price");
            entity.Property(e => e.WholesalePriceCurrencyCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Wholesale Price Currency Code");
            entity.Property(e => e.WholesalePriceIncludedVat)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Wholesale Price Included VAT");
            entity.Property(e => e.WholesalePriceIncludedVatCurrencyCode)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Wholesale Price Included VAT Currency Code");
            entity.Property(e => e.Width).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.WipCompletion)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("WIP Completion %");
            entity.Property(e => e.WipQuantity)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("WIP Quantity");
        });

        modelBuilder.Entity<ItemUnitOfMeasure>(entity =>
        {
            entity.HasKey(e => new
            {
                e.ItemNo, e.Code
            });

            entity.ToTable("Item Unit of Measure");

            entity.Property(e => e.ItemNo)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("Item No_");
            entity.Property(e => e.Code)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.BaseWeight)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Base Weight");
            entity.Property(e => e.Cubage).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Description)
                .HasMaxLength(160)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Diameter).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Height).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.LastDateModified)
                .HasColumnType("datetime")
                .HasColumnName("Last Date Modified");
            entity.Property(e => e.Length).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.LoginId)
                .HasMaxLength(50)
                .UseCollation("Latin1_General_CI_AS")
                .HasColumnName("LoginID");
            entity.Property(e => e.OracleSyncStatus).HasColumnName("oracle_sync_status");
            entity.Property(e => e.QtyPerUnitOfMeasure)
                .HasColumnType("decimal(28, 10)")
                .HasColumnName("Qty_ per Unit of Measure");
            entity.Property(e => e.RowId)
                .ValueGeneratedOnAdd()
                .HasColumnName("RowID");
            entity.Property(e => e.Thick).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Weight).HasColumnType("decimal(28, 10)");
            entity.Property(e => e.Width).HasColumnType("decimal(28, 10)");
        });

        modelBuilder.Entity<ScanProduct>(entity =>
        {
            entity.HasKey(e => e.ProductId).HasName("SCanProducts_PK");

            entity.ToTable("SCanProducts");

            entity.Property(e => e.CreateBy).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("datetime");
            entity.Property(e => e.ItemCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.UnitOfMeasure).HasMaxLength(100);

            entity.HasOne(d => d.ItemCodeNavigation).WithMany(p => p.ScanProducts)
                .HasForeignKey(d => d.ItemCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("SCanProducts_Item_FK");
        });

        modelBuilder.Entity<ScanProductV2>(entity =>
        {
            entity.HasKey(e => new
            {
                e.WarehouseId, e.ItemCode, e.CreateAt
            }).HasName("ScanProductV2_PK");

            entity.ToTable("ScanProductV2");

            entity.Property(e => e.ItemCode)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CreateAt).HasColumnType("datetime");
            entity.Property(e => e.CreateBy).HasMaxLength(100);

            entity.HasOne(d => d.ItemCodeNavigation).WithMany(p => p.ScanProductV2s)
                .HasForeignKey(d => d.ItemCode)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("ScanProductV2_Item_FK");

            entity.HasOne(d => d.Warehouse).WithMany(p => p.ScanProductV2S)
                .HasForeignKey(d => d.WarehouseId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("ScanProductV2_Warehouse_FK");
        });

        modelBuilder.Entity<UnitOfMeasure>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Unit of Measure");

            entity.Property(e => e.Code)
                .HasMaxLength(100)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Description)
                .HasMaxLength(200)
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.RowId)
                .ValueGeneratedOnAdd()
                .HasColumnName("RowID");
        });

        modelBuilder.Entity<Warehouse>(entity =>
        {
            entity.HasKey(e => e.WarehouseId).HasName("Warehouse_PK");

            entity.ToTable("Warehouse");

            entity.Property(e => e.CreateAt).HasColumnType("datetime");
            entity.Property(e => e.CreateBy).HasMaxLength(100);
            entity.Property(e => e.LocationCode).HasMaxLength(100);
        });
    }
}
