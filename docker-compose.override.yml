version: '3.4'

services:
    scanproduct.api:
        environment:
            - ASPNETCORE_ENVIRONMENT=Development
            - ASPNETCORE_HTTP_PORTS=8080
        ports:
            - "8080"
        volumes:
            - ${APPDATA}/Microsoft/UserSecrets:/home/<USER>/.microsoft/usersecrets:ro
            - ${APPDATA}/ASP.NET/Https:/home/<USER>/.aspnet/https:ro
    scanproduct.ui:
        environment:
            - ASPNETCORE_ENVIRONMENT=Development
            - ASPNETCORE_HTTP_PORTS=8080
        ports:
            - "8080"

