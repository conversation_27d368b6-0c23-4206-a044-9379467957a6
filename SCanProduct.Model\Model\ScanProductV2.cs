﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace SCanProduct.Model.Model;

[PrimaryKey("WarehouseId", "ItemCode")]
[Table("ScanProductV2")]
public class ScanProductV2
{
    [Key]
    public int WarehouseId { get; set; }

    [Key]
    [StringLength(100)]
    public string ItemCode { get; set; } = null!;

    [StringLength(100)]
    public string? CreateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public bool Block { get; set; }

    [ForeignKey("ItemCode")]
    [InverseProperty("ScanProductV2s")]
    public Item ItemCodeNavigation { get; set; } = null!;

    [ForeignKey("WarehouseId")]
    [InverseProperty("ScanProductV2S")]
    public Warehouse Warehouse { get; set; } = null!;
}
