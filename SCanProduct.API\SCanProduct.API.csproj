<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>55c48db3-e37b-4026-b824-af4629a28fb3</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1"/>
    <PackageReference Include="ClosedXML" Version="0.104.1"/>
    <PackageReference Include="FluentValidation" Version="11.11.0"/>
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0"/>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.2"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.7"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7"/>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="7.3.1"/>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.3.1"/>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1"/>
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.3"/>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0"/>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
    <PackageReference Include="Serilog" Version="4.0.1"/>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Controllers\"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SCanProduct.Model\SCanProduct.Model.csproj"/>
  </ItemGroup>

</Project>
