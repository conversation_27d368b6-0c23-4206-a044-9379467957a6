﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
namespace SCanProduct.UI.Models;

public class ProductInfoV2
{
    [DisplayName("Mã hàng")]
    [Required(ErrorMessage = "Vui lòng quét mã hãng.")]
    public string ItemNo { get; set; } = null!;
    [DisplayName("Location")]
    [Required(ErrorMessage = "Vui lòng quét Location.")]
    [RegularExpression(@"^[A-Z]{1,2}\d{2}-\d{3,4}$", ErrorMessage = "Location không đúng.")]
    public string LocationCode { get; set; } = null!;
    public int Location { get; set; }
}
