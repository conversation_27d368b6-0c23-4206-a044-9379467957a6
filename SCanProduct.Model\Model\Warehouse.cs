﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace SCanProduct.Model.Model;

[Table("Warehouse")]
public class Warehouse
{
    [Key]
    public int WarehouseId { get; set; }

    [StringLength(100)]
    public string? LocationCode { get; set; }

    [StringLength(100)]
    public string? CreateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? CreateAt { get; set; }

    public int? Location { get; set; }
    public bool? Block { get; set; }

    [InverseProperty("Warehouse")]
    public virtual ICollection<ScanProductV2> ScanProductV2S { get; set; } = new List<ScanProductV2>();
}
