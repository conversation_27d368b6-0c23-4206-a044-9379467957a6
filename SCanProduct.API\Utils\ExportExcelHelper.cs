using ClosedXML.Excel;
namespace SCanProduct.API.Utils;

public class ExcelExportOptions
{
    public string SheetName { get; set; } = "Sheet1";
    public bool AutoFilter { get; set; } = true;
}
public static class ExportExcelHelper
{

    public static string ExportToExcel<T>(List<string> headers, List<T> dataList, ExcelExportOptions? options = null)
    {
        options ??= new ExcelExportOptions();// Nếu không truyền options thì dùng mặc định
        using var workbook = new XLWorkbook();
        var worksheet = workbook.Worksheets.Add(options.SheetName);
        // Thêm header
        for (var i = 0; i < headers.Count; i++)
        {
            worksheet.Cell(1, i + 1).Value = headers[i];
            worksheet.Cell(1, i + 1).Style
                .Font.SetFontSize(13)
                .Font.SetFontColor(XLColor.WhiteSmoke)
                .Fill.SetBackgroundColor(XLColor.Green)
                .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
        }
        for (var row = 0; row < dataList.Count; row++)
        {
            var data = dataList[row];
            var properties = typeof(T).GetProperties();
            for (var col = 0; col < properties.Length && col < headers.Count; col++)
            {
                var value = properties[col].GetValue(data);
                worksheet.Cell(row + 2, col + 1).Value = value?.ToString() ?? "";
            }
        }
        if (options.AutoFilter && worksheet.RangeUsed() is not null)
        {
            worksheet.RangeUsed()?.SetAutoFilter();
        }
        worksheet.Columns().AdjustToContents();
        using var stream = new MemoryStream();
        workbook.SaveAs(stream);
        return Convert.ToBase64String(stream.ToArray());
    }
}
