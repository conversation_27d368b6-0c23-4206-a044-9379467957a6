﻿using System.Diagnostics.CodeAnalysis;
using System.Net.Http.Headers;
using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Newtonsoft.Json;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
using SCanProduct.UI.Models;
using SCanProduct.UI.Services;
using static SCanProduct.UI.Models.ProductInfo;
using Console=System.Console;
namespace SCanProduct.UI.Components.Pages;

public partial class Index : ComponentBase
{
    private List<ImageModel> ImagesData { get; set; } = [];
    private List<IBrowserFile> ListImageFile
    {
        get;
    } = [];
    private ProductInfo CurrentItemInfo { get; set; } = new ProductInfo
    {
        //ItemNo = "",
        //VendorBarCode = "",
        //UOM = "",
        Width = 1, Height = 1, Weight = 1, Length = 1
    };
    private List<string> ListImageToPreview { get; set; } = [];
    private ValidateForm? ValidateFormRef { get; set; }
    [Inject][NotNull] private ToastService? ToastService { get; set; }
    [Inject]
    private IProductService ProductService { get; set; } = default!;
    private Select<string>? SelectedUomRef { get; set; }
    [NotNull]
    private Modal? PreviewImageModal { get; set; }
    private List<DetailScanProductDto> ListProductInfo { get; set; } = [];
    private ItemInfoDto? FoundItemInfo { get; set; } = new ItemInfoDto();
    private BootstrapInput<string> VendorBarCodeRef { get; set; } = new BootstrapInput<string>();
    private BootstrapInputNumber<int> LengthRef { get; set; } = new BootstrapInputNumber<int>();
    private bool IsLoading { get; set; }
    private string ValidateFormId
    {
        get;
    } = new Guid().ToString();
    private IEnumerable<SelectedItem> ListUomToSelect { get; set; } = [];
    protected override void OnInitialized()
    {
        IsLoading = true;
        base.OnInitialized();
        IsLoading = false;
    }
    private async Task OnItemCodeChanged()
    {
        try
        {
            if (CurrentItemInfo.ItemNo != "")
            {
                var listUom = await ProductService.GetUomByItemNo(CurrentItemInfo.ItemNo);
                if (listUom.Count == 0)
                {
                    return;
                }
                FoundItemInfo = new ItemInfoDto
                {
                    ListUnitOfMeasure = listUom
                };
                ListProductInfo = [];
                var listProductAddedByItemId = await ProductService.GetListProductsByItemNumber(CurrentItemInfo.ItemNo);
                if (listProductAddedByItemId.Count > 0)
                {
                    var listUomAdded = listProductAddedByItemId.Select(x => x.UnitOfMeasure).ToList();
                    var listUomCanAdd = listUom.Except(listUomAdded).ToList();
                    //set giá trị mặc định cho select uom
                    CurrentItemInfo.VendorBarCode = listProductAddedByItemId[0].BarCode;
                    if (listUomCanAdd.Count != 0)
                    {
                        var currentSelectedUom = listUomCanAdd[0];
                        ListUomToSelect = listUomCanAdd.Select(x => new SelectedItem(x, x));
                        CurrentItemInfo.UOM = currentSelectedUom;
                    }
                    else
                    {
                        ListUomToSelect = new List<SelectedItem>();
                        ValidateFormRef!.SetError<ProductInfo>(expression: p => p.ItemNo, "Sản phẩm đã được thêm toàn bộ vào CSDL");
                        CurrentItemInfo.UOM = "";
                    }
                    ListProductInfo = listProductAddedByItemId;
                }
                else
                {// product not added yet
                    var currentSelectedUom = FoundItemInfo!.ListUnitOfMeasure[0];
                    var listUomToSelectTemp = FoundItemInfo.ListUnitOfMeasure.Select(x => new SelectedItem(x, x));
                    ListUomToSelect = listUomToSelectTemp;
                    CurrentItemInfo.UOM = currentSelectedUom;
                }
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await ToastService.Error(ex.Message);
        }
    }
    private async Task OnAddData(EditContext context)
    {
        var rq = new CreateProductModel
        {
            ItemCode = CurrentItemInfo.ItemNo,
            BarCode = CurrentItemInfo.VendorBarCode,
            UnitOfMeasure = CurrentItemInfo.UOM,
            Length = CurrentItemInfo.Length,
            Width = CurrentItemInfo.Width,
            Height = CurrentItemInfo.Height,
            Weight = CurrentItemInfo.Weight,
            CreateBy = "Web"
        };
        var product = await ProductService.CreateProduct(rq);
        {
            try
            {//parse to model
                var productCreated = JsonConvert.DeserializeObject<ScanProduct>(product);
                if (ImagesData.Count != 0 && ListImageFile.Count != 0)// upload image
                {
                    var formData = new MultipartFormDataContent();
                    ListImageFile.ForEach(x =>
                    {
                        var streamContent = new StreamContent(x.OpenReadStream(209715200));
                        streamContent.Headers.ContentType = new MediaTypeHeaderValue(x.ContentType);
                        formData.Add(streamContent, "files", x.Name);
                    });
                    formData.Add(new StringContent(productCreated!.ProductId.ToString()), "productId");
                    var result = await ProductService.UploadProductImage(formData);
                    if (!result.Contains("productId"))
                    {
                        await ToastService.Information("Hệ thống", result);// upload image message
                    }
                }
                CurrentItemInfo = new ProductInfo
                {
                    UOM = ""
                };
                ListUomToSelect = [];
                ListImageToPreview = [];
                ListProductInfo = [];
                ImagesData = [];
            }
            catch
            {
                await ToastService.Warning("Hệ thống", product);// create product message
            }
        }
        await ToastService.Success("Hệ thống", "Tạo sản phẩm thành công!");
        //navigationManager.Refresh(true);
        StateHasChanged();
    }
    //private void OnClickToClearForm() { isFormClearing = true; }
    private void OnClickToPreviewImg(int productId)
    {
        ListImageToPreview = ListProductInfo.Find(x => x.ProductId == productId)!.Images.Select(x => x.Url).ToList()!;
        PreviewImageModal.Toggle();
    }
    private async Task OnClickToUpload(UploadFile file)
    {
        try
        {
            if (file.File != null)
            {
                if (file.Size > 2 * 1024 * 1024)
                {
                    await ToastService.Information("Upload the file", "Vui lòng tải lên ảnh có size dưới 2MB");
                    file.Code = 1;
                    file.Error = "Vui lòng tải lên ảnh có size dưới 2MB";
                }
                else
                {
                    var fileData = file.File;
                    //var fileData = await fileData!.RequestImageFileAsync("image/png", 1000, 1000);
                    var buffer = new byte[fileData.Size];
                    var imageData = $"data:image/png;base64,{Convert.ToBase64String(buffer)}";
                    ListImageFile.Add(fileData);
                    ImagesData.Add(new ImageModel
                    {
                        Name = file.OriginFileName!, Base64 = imageData
                    });
                    //}
                    await ToastService.Success("Title", $"Tải lên file {file.OriginFileName} thành công!");
                    StateHasChanged();
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
        }
    }
    private async Task<bool> RemoveImagePreview(UploadFile file)
    {
        try
        {
            var foundImage = ImagesData.Find(x => x.Name == file.OriginFileName!);
            if (foundImage != null)
            {
                ImagesData.Remove(foundImage);
            }
            await Task.CompletedTask;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return false;
        }
        return true;
    }
}
