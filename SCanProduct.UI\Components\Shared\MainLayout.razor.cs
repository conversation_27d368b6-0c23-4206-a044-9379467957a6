using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components.Routing;
namespace SCanProduct.UI.Components.Shared;

/// <summary>
/// </summary>
public sealed partial class MainLayout
{
    private bool UseTabSet { get; set; } = true;

    private string Theme
    {
        get;
    } = "";

    private bool IsOpen { get; set; }

    private bool IsFixedHeader { get; set; } = true;

    private bool IsFixedFooter { get; set; } = true;

    private bool IsFullSide { get; set; } = true;

    private bool ShowFooter { get; set; } = true;

    private List<MenuItem>? Menus { get; set; }

    /// <summary>
    ///     OnInitialized 方法
    /// </summary>
    protected override void OnInitialized()
    {
        base.OnInitialized();

        Menus = GetIconSideMenuItems();
    }

    private static List<MenuItem> GetIconSideMenuItems()
    {
        var menus = new List<MenuItem>
        {
            new MenuItem
            {
                Text = "Trang chủ", Icon = "fa-solid fa-fw fa-flag", Url = "/", Match = NavLinkMatch.All
            },
            new MenuItem { Text = "Danh sách", Icon = "fa-solid fa-fw fa-flag", Url = "/list-product" , Match = NavLinkMatch.All},
            new MenuItem { Text = "Warehouse Scan", Icon = "fa-solid fa-fw fa-flag", Url = "/warehouse" , Match = NavLinkMatch.All},
        };

        return menus;
    }
}
