﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SCanProduct.Model.Dto;

public class DetailScanProductV2Dto
{
    public int ProductId { get; set; }
    [StringLength(100)]
    [DisplayName("Mã hàng")]
    public string ItemCode { get; set; } = null!;
    [StringLength(100)]
    [DisplayName("Barcode của NCC")]
    public string BarCode { get; set; } = null!;
    [StringLength(100)]
    [DisplayName("LocationCode")]
    public string LocationCode { get; set; } = null!;
    [StringLength(100)]
    [DisplayName("WarehouseId")]
    public string WarehouseId { get; set; } = null!;

    [StringLength(100)]
    [DisplayName("Đơn vị")]
    public string UnitOfMeasure { get; set; } = null!;

    [DisplayName("Ngày tạo")][Column(TypeName = "datetime")] public DateTime CreateDate { get; set; }
    [DisplayName("Người tạo")][StringLength(50)] public string? CreateBy { get; set; }
    public bool Block { get; set; }

    // public virtual ICollection<DetailImageDto> Images { get; set; } = new List<DetailImageDto>();
}


public class WareHouseDto
{
    public string WarehouseId { get; set; }
    public string LocationCode { get; set; }
    public string CreateBy { get; set; }
    public string CreateAt { get; set; }
    public string Block { get; set; }

    public List<DetailScanProductV2Dto> Details { get; set; }
}
