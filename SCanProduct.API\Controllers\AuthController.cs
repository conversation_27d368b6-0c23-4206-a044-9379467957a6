using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
namespace SCanProduct.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController : ControllerBase
{
    private readonly IConfiguration _configuration;

    public AuthController(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    [HttpPost("login")]
    public async Task<AuthLoginResponse> Login([FromBody] LoginDto loginDto)
    {
        // TODO: Thay thế bằng logic xác thực thực tế với database
        if (loginDto.Username != "admin" || loginDto.Password != "admin")
        {
            await Task.Delay(500);
            return new AuthLoginResponse
            {
                Success = false, Message = "Tên đăng nhập hoặc mật khẩu không đúng"
            };
        }
        var token = GenerateJwtToken(loginDto.Username);
        return new AuthLoginResponse
        {
            Success = true, Token = token, Message = "Đăng nhập thành công"
        };

    }

    private string GenerateJwtToken(string username)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "your-256-bit-secret-key-here"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.Name, username),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
        };

        var token = new JwtSecurityToken(
        _configuration["Jwt:Issuer"] ?? "SCanProduct",
        _configuration["Jwt:Audience"] ?? "SCanProduct",
        claims,
        expires: DateTime.UtcNow.AddHours(1),
        signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}
