﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SCanProduct.Model.Model;

[PrimaryKey("ItemNo", "Code")]
[Table("Item Unit of Measure")]
public class ItemUnitOfMeasure
{
    [Key]
    [Column("Item No_")]
    [StringLength(100)]
    public string ItemNo { get; set; } = null!;

    [Key] [StringLength(100)] public string Code { get; set; } = null!;

    [Column("Qty_ per Unit of Measure", TypeName = "decimal(28, 10)")]
    public decimal QtyPerUnitOfMeasure { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal Length { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal Width { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal Height { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal Cubage { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal Weight { get; set; }

    [StringLength(160)] public string? Description { get; set; }

    public int? Type { get; set; }

    public int? Status { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal? Diameter { get; set; }

    [Column(TypeName = "decimal(28, 10)")] public decimal? Thick { get; set; }

    [Column("Base Weight", TypeName = "decimal(28, 10)")]
    public decimal? BaseWeight { get; set; }

    [Column("RowID")] public int RowId { get; set; }

    [Column("Last Date Modified", TypeName = "datetime")]
    public DateTime? LastDateModified { get; set; }

    [Column("LoginID")] [StringLength(50)] public string? LoginId { get; set; }

    public int? Block { get; set; }

    [Column("oracle_sync_status")] public short? OracleSyncStatus { get; set; }
}