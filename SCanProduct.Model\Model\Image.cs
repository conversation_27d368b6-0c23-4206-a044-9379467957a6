﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SCanProduct.Model.Model;

public class Image
{
    [Key] public int ImageId { get; set; }

    public int ProductId { get; set; }

    [StringLength(50)] public string? Type { get; set; }

    [StringLength(50)] public string? Name { get; set; }

    [StringLength(50)] public string? Size { get; set; }

    [StringLength(500)] public string? Url { get; set; }

    [ForeignKey("ProductId")]
    [InverseProperty("Images")]
    public virtual ScanProduct Product { get; set; } = null!;
}