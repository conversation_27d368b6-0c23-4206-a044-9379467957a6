﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="Locales\en.json"/>
    <Content Remove="Locales\zh.json"/>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1"/>
    <PackageReference Include="Blazored.LocalStorage" Version="4.5.0"/>
    <PackageReference Include="BootstrapBlazor" Version="8.*"/>
    <PackageReference Include="BootstrapBlazor.BarCode" Version="8.0.1"/>
    <PackageReference Include="BootstrapBlazor.FontAwesome" Version="8.*"/>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1"/>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.3.1"/>
    <PackageReference Include="System.Net.Http" Version="4.3.4"/>
    <PackageReference Include="System.Net.Http.Json" Version="8.0.0"/>
    <PackageReference Include="ZXingBlazor" Version="1.1.5"/>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SCanProduct.Model\SCanProduct.Model.csproj"/>
  </ItemGroup>

  <ItemGroup>
    <_ContentIncludedByDefault Remove="Locales\en.json"/>
    <_ContentIncludedByDefault Remove="Locales\zh.json"/>
  </ItemGroup>

</Project>
