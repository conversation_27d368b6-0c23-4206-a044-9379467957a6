﻿using System.Diagnostics.CodeAnalysis;
using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using SCanProduct.Model;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Extensions;
using SCanProduct.UI.Models;
using SCanProduct.UI.Services;
namespace SCanProduct.UI.Components.Pages;

public partial class AddProductV2 : ComponentBase
{
    protected Table<WarehouseProductDto> TableProductRef { get; set; } = new Table<WarehouseProductDto>();
    protected ProductInfoV2 CurrentItemInfo { get; set; } = new ProductInfoV2
    {
        LocationCode = string.Empty, ItemNo = string.Empty
    };


    [Inject]
    protected IProductService ProductService { get; set; } = default!;
    [Inject]
    protected ToastService ToastService { get; set; } = default!;
    [Inject] public IJSRuntime JsRuntime { get; set; } = default!;
    protected List<WarehouseProductDto> ListProductInfo { get; set; } = [];
    protected int PageNumber { get; set; } = 1;
    protected int PageSize { get; set; } = 25;
    protected int TotalCount { get; set; }

    protected static IEnumerable<int> PageItemsSource =>
    [
        25, 50, 100
    ];
    protected ValidateForm? ValidateFormRef { get; set; }
    protected BootstrapInput<string> LocationCodeRef { get; set; } = new BootstrapInput<string>();
    protected BootstrapInput<string> SearchLocationCodeRef { get; set; } = new BootstrapInput<string>();
    protected BootstrapInput<string> SearchItemCodeRef { get; set; } = new BootstrapInput<string>();
    protected BootstrapInput<string> SearchItemNameRef { get; set; } = new BootstrapInput<string>();
    protected BootstrapInput<string> ItemNoRef { get; set; } = new BootstrapInput<string>();
    protected Button SubmitButtonRef { get; set; } = new Button();
    protected string ItemNumberToSearch { get; set; } = "";
    protected string ItemNameToSearch { get; set; } = "";
    protected string LocationToSearch { get; set; } = "";
    protected bool IsLoading { get; set; }
    protected bool IsUpdate { get; set; }
    protected string RespMsg { get; set; } = "";
    [NotNull]
    protected Modal? Modal { get; set; }
    protected bool ShowScanItemCode { get; set; }
    protected bool ShowScanLocationCode { get; set; }
    protected string ValidateFormId { get; set; } = new Guid().ToString();
    protected string SelectedLocation { get; set; } = "";
    protected string SelectedLocationText { get; set; } = "";
    protected List<SegmentedOption<string>> Items { get; } =
    [
        new SegmentedOption<string>
        {
            Value = "Daily", Text = "Daily"
        },
        new SegmentedOption<string>
        {
            Value = "Weekly", Text = "Weekly"
        },
        new SegmentedOption<string>
        {
            Value = "Monthly", Text = "Monthly"
        },
        new SegmentedOption<string>
        {
            Value = "Quarterly", Text = "Quarterly"
        },
        new SegmentedOption<string>
        {
            Value = "Yearly", Text = "Yearly"
        }
    ];

    protected static IEnumerable<SegmentedOption<string>> SegmentedLocations { get; set; } = new List<SegmentedOption<string>>();
    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        LoadSegmentLocation();
        await LoadListProduct();
        await base.OnInitializedAsync();
        IsLoading = false;
    }

    protected void LoadSegmentLocation()
    {
        var enums = CustomEnumExtensions.GetDisplayNamesWithValues<LocationEnum>();
        var items = enums.Select(e => new SegmentedOption<string>
            {
                Value = e.Value, Text = e.Key
            })
            .ToList();
        SegmentedLocations = new List<SegmentedOption<string>>(items);
        SelectedLocation = SegmentedLocations.FirstOrDefault()!.Value!;
        SelectedLocationText = SegmentedLocations.FirstOrDefault()!.Text!;
    }

    protected async Task OnSearchLocationCodeChanged(string value)
    {
        try
        {
            CurrentItemInfo.LocationCode = value.Trim();
            LocationToSearch = value.Trim();
            await TableProductRef.QueryAsync();
        }

        catch (Exception ex)
        {
            await ToastService.Error("Hệ thống",
            $"Không tìm thấy location có mã {value}. Nguyên nhân: " + ex.GetBaseException().Message);
        }
    }
    protected async Task OnSearchItemNameChanged(string value)
    {
        try
        {
            ItemNameToSearch = value.Trim();
            await TableProductRef.QueryAsync();
        }

        catch (Exception ex)
        {
            await ToastService.Error("Hệ thống", $"Không tìm thấy item có mã {value}. Nguyên nhân: " + ex.GetBaseException().Message);
        }
    }
    protected async Task OnSearchItemCodeChanged(string value)
    {
        try
        {
            ItemNumberToSearch = value.Trim();
            await TableProductRef.QueryAsync();
        }

        catch (Exception ex)
        {
            await ToastService.Error("Hệ thống", $"Không tìm thấy item có mã {value}. Nguyên nhân: " + ex.GetBaseException().Message);
        }
    }

    protected async Task OnAddOrUpdateProduct()
    {
        try
        {
            if (string.IsNullOrEmpty(CurrentItemInfo.ItemNo))
            {
                await ItemNoRef.FocusAsync();
                return;
            }
            var rq = new ScanProductV2Dto
            {
                ScanCode = CurrentItemInfo.ItemNo.Trim(),
                LocationCode = CurrentItemInfo.LocationCode.Trim(),
                CreateBy = "WEB",
                Location = CurrentItemInfo.Location,
                SaveAs = IsUpdate// false -> tạo mới, true -> update
            };
            var resp = await ProductService.CreateProductV2(rq);
            if (resp is not null)
            {
                switch (resp.StatusCode)
                {
                    case 201:
                    // create or update success
                    case 204:
                        await ToastService.Success("Hệ thống", resp.Message);
                        CurrentItemInfo.ItemNo = string.Empty;
                        IsUpdate = false;
                        await TableProductRef.QueryAsync();
                        break;
                    case 400:
                    case 404:
                    case 500:
                    {
                        RespMsg = resp.Message;
                        if (resp.StatusCode == 400)
                        {
                            IsUpdate = true;// item đã được khai báo ở location khác
                        }
                        await Modal.Toggle();
                        StateHasChanged();
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            await ToastService.Error("Hệ thống", "Tạo sản phẩm thất bại. Nguyên nhân: " + ex.GetBaseException().Message);
        }
    }
    protected Task OnLocationValueChanged(string value)
    {
        CurrentItemInfo.Location = int.Parse(value);
        SelectedLocation = value;
        SelectedLocationText = SegmentedLocations.ToList().Find(x => x.Value == value)!.Text!;
        StateHasChanged();
        return Task.CompletedTask;
    }
    protected async Task LoadListProduct()
    {
        try
        {
            var resp = await ProductService.GetListProductsV2(PageNumber, PageSize, null, CurrentItemInfo.LocationCode);
            ListProductInfo = resp.TotalCount > 0 ? resp.Data : new List<WarehouseProductDto>();
            var existingProductLocation = ListProductInfo.FirstOrDefault()?.Location;

            if (existingProductLocation is not null)
            {
                await OnLocationValueChanged(existingProductLocation.Value.ToString());
            }
        }
        catch (Exception ex)
        {
            await ToastService.Error("Hệ thống", "Load product thất bại. Thử lại sau. Lỗi: " + ex.GetBaseException().Message);
        }
    }


    protected async Task<QueryData<WarehouseProductDto>> OnQueryAsync(QueryPageOptions options)
    {
        PageNumber = options.PageIndex;
        PageSize = options.PageItems;

        var resp = await ProductService.GetListProductsV2(PageNumber, PageSize, ItemNumberToSearch, ItemNameToSearch.ToLower(),
        LocationToSearch);

        ListProductInfo = resp.TotalCount > 0 ? resp.Data : [];

        var existingProductLocation = ListProductInfo.FirstOrDefault()?.Location;

        if (existingProductLocation is not null)
        {
            await OnLocationValueChanged(existingProductLocation.Value.ToString());
            SelectedLocation = existingProductLocation.Value.ToString();
        }
        var items = ListProductInfo;
        return new QueryData<WarehouseProductDto>
        {
            Items = items, TotalCount = TotalCount
        };
    }
    #region barcode scanner
    public string? BarCode { get; set; }

    protected async Task ScanResultLocation(string e)
    {
        CurrentItemInfo.LocationCode = e;
        IsUpdate = false;
        CurrentItemInfo.ItemNo = string.Empty;
        LocationToSearch = e;
        ShowScanLocationCode = !ShowScanLocationCode;
        await TableProductRef.QueryAsync();
        await ItemNoRef.FocusAsync();
    }
    protected async Task ScanResultItemCode(string e)
    {

        CurrentItemInfo.ItemNo = e;
        IsUpdate = false;
        ShowScanItemCode = !ShowScanItemCode;
        await OnAddOrUpdateProduct();
    }
    #endregion barcode scanner
}
