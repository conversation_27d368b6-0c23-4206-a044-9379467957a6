﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using SCanProduct.UI.Services;
namespace SCanProduct.UI.Components.Pages;
public partial class LogoutCustom
{
    [Inject]
    private ILocalStorageService? LocalStorage { get; set; }
    [Inject]
    NavigationManager? NavigationManager { get; set; }
    [Inject]
    IAuthService AuthService { get; set; } = default!;
    protected override void OnInitialized()
    {
        LogoutAsync();
        base.OnInitialized();
    }
    private void LogoutAsync()
    {
        AuthService.Logout();
        LocalStorage!.RemoveItemAsync("userName");
        NavigationManager!.NavigateTo("/auth/login");
    }
}
