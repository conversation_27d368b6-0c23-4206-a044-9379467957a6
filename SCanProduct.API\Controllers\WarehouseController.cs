using Microsoft.AspNetCore.Mvc;
using SCanProduct.API.Repositories.Impl;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
namespace SCanProduct.API.Controllers;

[Route("api/v2/[controller]")]
[ApiController]
public class WarehouseController : ControllerBase
{
    private readonly IWarehouseRepository _warehouseRepository;
    public WarehouseController(IWarehouseRepository warehouseRepository)
    {
        _warehouseRepository = warehouseRepository;
    }

    [HttpPost("scan-product")]
    public async Task<ApiResponse> ScanProduct(ScanProductV2Dto scanProductV2Dto)
    {
        return await _warehouseRepository.ScanningProduct(scanProductV2Dto);
    }

    [HttpGet("products")]
    public async Task<IActionResult> GetProducts(int pageNumber = 1, int pageSize = 10, string itemNumber = "", string itemName = "", string locationCode = "")
    {
        var result = await _warehouseRepository.GetProducts(pageNumber, pageSize, itemNumber, itemName, locationCode);
        return Ok(result);
    }
}
