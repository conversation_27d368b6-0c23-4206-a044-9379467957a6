﻿using Newtonsoft.Json;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.UI.Models;
namespace SCanProduct.UI.Services;

public class ProductService : IProductService
{
    public async Task<bool> BlockProduct(int productId)
    {
        try
        {
            var responseApi =
                await Program.HttpClient.PostAsync($"/api/ScanProduct/block-scan-product-data?productId={productId}", null);
            responseApi.EnsureSuccessStatusCode();
            var responseBody = await responseApi.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ApiResponse>(responseBody);
            return result is { IsSuccessStatusCode: true };
        }
        catch
        {
            return false;
        }
    }

    public async Task<string> CreateProduct(CreateProductModel model)
    {
        var responseApi =
            await Program.HttpClient.PostAsJsonAsync("/api/ScanProduct/create-scan-product-data", model);
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<ApiResponse>(responseBody);
        return result?.Result.ToString() ?? "";
    }

    public async Task<object?> ExportAllProductToExcel()
    {
        var responseApi = await Program.HttpClient.GetAsync("/api/ScanProduct/export-all-product");
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        return responseBody;
    }

    public async Task<Pagination<DetailScanProductDto>> GetListProducts(int pageNumber = 1, int pageSize = 10,
        string? itemNumber = null)
    {
        var url = $"api/ScanProduct/get-scan-product-data?pageNumber={pageNumber}&pageSize={pageSize}&itemNumber=";
        if (itemNumber is not null)
        {
            url += itemNumber;
        }
        var responseApi = await Program.HttpClient.GetAsync(url);
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<ApiResponse<Pagination<DetailScanProductDto>>>(responseBody);
        return result!.Result;
    }

    public async Task<List<DetailScanProductDto>> GetListProductsByItemNumber(string itemNumber = "")
    {
        var responseApi = await Program.HttpClient.GetAsync("api/ScanProduct/get-scan-product-data-by-item-number/" + itemNumber);
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<ApiResponse<List<DetailScanProductDto>>>(responseBody);
        return result!.Result;
    }

    public async Task<List<string>> GetUomByItemNo(string itemNo)
    {
        var responseApi = await Program.HttpClient.GetAsync("api/ScanProduct/get-unit-measure-by-item-number/" + itemNo);
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<ApiResponse<List<string>>>(responseBody);
        return result!.Result;
    }

    public async Task<string> UploadProductImage(MultipartFormDataContent content)
    {
        try
        {
            var responseApi = await Program.HttpClient.PostAsync("api/ScanProduct/update-scan-product-image", content);
            responseApi.EnsureSuccessStatusCode();
            var responseBody = await responseApi.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ApiResponse<string>>(responseBody);
            return result!.Result;
        }
        catch (Exception ex)
        {
            return ex.GetBaseException().Message;
        }
    }
    #region v2
    public async Task<PaginationV2<WarehouseProductDto>> GetListProductsV2(int pageNumber = 1, int pageSize = 10,
        string? itemNumber = null, string? itemName = null, string? locationCode = null)
    {
        var url = $"api/v2/Warehouse/products?pageNumber={pageNumber}&pageSize={pageSize}";
        if (!string.IsNullOrEmpty(itemNumber))
        {
            url += $"&itemNumber={itemNumber}";
        }
        if (!string.IsNullOrEmpty(itemName))
        {
            url += $"&itemName={itemName}";
        }
        if (!string.IsNullOrEmpty(locationCode))
        {
            url += $"&locationCode={locationCode}";
        }
        var rs = await Program.HttpClient.GetAsync(url);
        var content = rs.Content;
        var products = await content.ReadFromJsonAsync<PaginationV2<WarehouseProductDto>>();
        return products!;
    }
    public async Task<ApiResponse?> CreateProductV2(ScanProductV2Dto model)
    {
        var responseApi = await Program.HttpClient.PostAsJsonAsync("api/v2/Warehouse/scan-product", model);
        responseApi.EnsureSuccessStatusCode();
        var responseBody = await responseApi.Content.ReadAsStringAsync();
        var result = JsonConvert.DeserializeObject<ApiResponse>(responseBody);
        return result;
    }
    #endregion v2
}
