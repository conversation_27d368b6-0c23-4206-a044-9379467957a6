{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "SCanProduct.UI": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "http://localhost:5200"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true}}, "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:53189", "sslPort": 0}}}