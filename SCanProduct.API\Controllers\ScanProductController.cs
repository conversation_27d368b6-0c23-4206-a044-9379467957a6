using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SCanProduct.API.Data;
using SCanProduct.API.Utils;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
namespace SCanProduct.API.Controllers;

[Route("api/[controller]")]
[ApiController]
public class ScanProductController : ControllerBase
{
    private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);
    private readonly ApplicationDbContext _context;
    private readonly FileUploadServices _fileUploadServices;
    private readonly IMapper _mapper;

    public ScanProductController(ApplicationDbContext context, IMapper mapper, FileUploadServices fileUploadServices)
    {
        _context = context;
        _mapper = mapper;
        _fileUploadServices = fileUploadServices;
    }
    [HttpPost("create-scan-product-data")]
    public async Task<ApiResponse> CreateScanProduct([FromForm] CreateScanProductDto productDto, [FromForm] List<IFormFile> files)
    {
        await _semaphore.WaitAsync();
        try
        {
            if (files.Count == 0)
            {
                return new ApiResponse(404, "Image is required");
            }

            var itemCheck = await _context.Items.Where(h => h.No == productDto.ItemCode).FirstOrDefaultAsync();
            if (itemCheck is null)
            {
                return new ApiResponse(404, "Item not found");
            }

            var unitCheck = await _context.ItemUnitOfMeasures.Where(h => h.ItemNo == productDto.ItemCode
                                                                         && h.Code == productDto.UnitOfMeasure).FirstOrDefaultAsync();
            if (unitCheck is null)
            {
                return new ApiResponse(404, "Unit of measure not found");
            }

            var checkProduct = await _context.ScanProducts
                .Where(h => h.ItemCode == productDto.ItemCode &&
                            h.UnitOfMeasure == productDto.UnitOfMeasure &&
                            h.Block == false).ToListAsync();
            if (checkProduct.Count != 0)
            {
                return new ApiResponse(404, "Product already exists");
            }

            if (productDto.Width <= 0 || productDto.Height <= 0 || productDto.Length <= 0 || productDto.Weight < 0)
            {
                return new ApiResponse(404, "Dimension or weight must be greater than 0");
            }

            await using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var product = _mapper.Map<ScanProduct>(productDto);
                product.CreateDate = DateTime.Now;
                product.Block = false;
                await _context.ScanProducts.AddAsync(product);
                await _context.SaveChangesAsync();

                foreach (var file in files)
                {
                    var statusUpload = await _fileUploadServices.UploadFileToExternalService(file, product.ProductId);
                    if (!statusUpload.IsSuccessStatusCode)
                    {
                        throw new Exception(statusUpload.Message);
                    }
                }

                await transaction.CommitAsync();

                var productResponseDto = _mapper.Map<ScanProductDto>(product);
                return new ApiResponse(200, "", productResponseDto);
            }
            catch (Exception e)
            {
                await transaction.RollbackAsync();
                return new ApiResponse(404, e.Message);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e.Message);
            return new ApiResponse(500, e.Message);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    [HttpGet("get-unit-measure-by-item-number/{itemNumber}")]
    public async Task<ApiResponse> GetUnitOfMeasure(string itemNumber)
    {
        var listUnit = await _context.ItemUnitOfMeasures
            .Where(x => x.ItemNo == itemNumber)
            .Select(x => x.Code)
            .ToListAsync();
        return new ApiResponse(200, "", listUnit);
    }

    [HttpGet("get-scan-product-data")]
    public async Task<ApiResponse> GetScanProduct(int pageNumber = 1, int pageSize = 10, string itemNumber = "")
    {
        if (pageNumber < 1)
        {
            pageNumber = 1;
        }
        if (pageSize < 1)
        {
            pageSize = 10;
        }

        var query = _context.ScanProducts.Where(h => h.Block == false)
            .Include(h => h.ItemCodeNavigation)
            .Include(h => h.Images)
            .AsQueryable();

        if (!string.IsNullOrEmpty(itemNumber))
        {
            query = query.Where(p => p.ItemCode.Contains(itemNumber));
        }

        var totalProducts = await query.CountAsync();

        var listProduct = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var productDto = _mapper.Map<List<DetailScanProductDto>>(listProduct);

        var result = new
        {
            TotalCount = totalProducts,
            PageSize = pageSize,
            CurrentPage = pageNumber,
            TotalPages = (int)Math.Ceiling(totalProducts / (double)pageSize),
            Products = productDto
        };
        return new ApiResponse(200, "", result);
    }

    [HttpGet("get-scan-product-data-by-item-number/{itemNumber}")]
    public async Task<ApiResponse> GetScanProductByItemNumber(string itemNumber)
    {
        var listProduct = await _context.ScanProducts.Where(x => x.ItemCode == itemNumber && x.Block == false)
            .Include(h => h.ItemCodeNavigation)
            .Include(h => h.Images)
            .ToListAsync();

        var productDto = _mapper.Map<List<DetailScanProductDto>>(listProduct);
        return new ApiResponse(200, "", productDto);
    }

    [HttpPost("block-scan-product-data")]
    public async Task<ApiResponse> BlockProduct(int productId)
    {
        if (productId <= 0)
        {
            return new ApiResponse(404, "Product ID không hợp lệ");
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var product = await _context.ScanProducts
                .FirstOrDefaultAsync(h => h.ProductId == productId);
            if (product is null)
            {
                return new ApiResponse(404, "Không tìm thấy sản phẩm");
            }

            if (product.Block)
            {
                return new ApiResponse(404, "Sản phẩm đã bị khóa");
            }

            product.Block = true;
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();
            return new ApiResponse(200, "Xóa sản phẩm thành công");
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            Console.WriteLine(ex.Message);
            return new ApiResponse(500, "Có lỗi xảy ra khi xóa sản phẩm");
        }
    }

    [HttpGet("export-all-product")]
    public IActionResult ExportAllProductToExcel()
    {
        try
        {
            var query = _context.ScanProducts.AsNoTracking().Where(h => h.Block == false)
                .Include(h => h.ItemCodeNavigation);
            var dataExcel = query.ToList()
                .Where(x => x.ItemCodeNavigation.Name != null)
                .Select(x => new ExportAllProductDto
                {
                    ItemCode = x.ItemCode,
                    ItemName = x.ItemCodeNavigation.Name ?? "",
                    UnitOfMeasure = x.UnitOfMeasure,
                    Length = x.Length.ToString(),
                    Width = x.Width.ToString(),
                    Height = x.Height.ToString(),
                    Weight = x.Weight.ToString(),
                    CreateDate = x.CreateDate.ToString()
                })
                .ToList();

            var excelOpts = new ExcelExportOptions
            {
                AutoFilter = true, SheetName = "Danh sách SKUs " + DateTime.UtcNow.AddHours(7).ToString("dd-MM-yyyy")
            };
            var headers = new List<string>
            {
                "Mã sản phẩm",
                "Tên sản phẩm",
                "Đơn vị",
                "Chiều dài",
                "Chiều rộng",
                "Chiều cao",
                "Trọng lượng",
                "Ngày tạo"
            };
            var fileData = ExportExcelHelper.ExportToExcel(headers, dataExcel, excelOpts);
            return Ok(fileData);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return StatusCode(500, new
            {
                Message = "Có lỗi xảy ra khi export dữ liệu"
            });
        }
    }
}
