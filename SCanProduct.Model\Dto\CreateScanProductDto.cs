﻿using System.ComponentModel.DataAnnotations;
namespace SCanProduct.Model.Dto;

public class CreateScanProductDto
{
    [StringLength(100)]
    public string ItemCode { get; set; } = null!;

    [StringLength(100)]
    public string UnitOfMeasure { get; set; } = null!;

    public int Length { get; set; }

    public int Width { get; set; }

    public int Height { get; set; }

    public int Weight { get; set; }

    [StringLength(50)]
    public string? CreateBy { get; set; }
}
