﻿@page "/scan-product"
@using SCanProduct.Model.Dto
@attribute [Authorize]

@if (IsLoading)
{
    <p>Loading...</p>
}
else
{
    <div class="card" style="min-height:83vh">
        <div class="card-body">
            <div class="row">
                <div class="col-sm-5">
                    <ValidateForm @ref="ValidateFormRef" Model="@CurrentItemInfo" Id="@ValidateFormId" DisableAutoSubmitFormByEnter="true"
                                  OnValidSubmit="@OnAddData">
                        <DataAnnotationsValidator/>
                        <div class="row">
                            @*<ValidationSummary />*@
                            <div class="col-12 col-sm-4">
                                <BootstrapInput TValue="String"
                                                IsTrim
                                                IsAutoFocus
                                                @onfocusout="@OnItemCodeChanged"
                                                PlaceHolder="Mã SKU"
                                                ShowLabel="true"
                                                @bind-Value="@CurrentItemInfo.ItemNo"
                                                DisplayText="Quét mã barcode/QRCode"/>
                            </div>
                            <div class="col-12 col-sm-4">
                                <Select @ref="SelectedUomRef"
                                        PlaceHolder="Đơn vị"
                                        DisplayText="Đơn vị "
                                        @onfocusout="@(async _ =>
                                                     {
                                                         await VendorBarCodeRef.FocusAsync();
                                                         StateHasChanged();
                                                     })"
                                        NoSearchDataText="Không có dữ liệu"
                                        @bind-Value="@CurrentItemInfo.UOM"
                                        ShowLabel="true"
                                        Items="ListUomToSelect"></Select>
                            </div>
                            <div class="col-12 col-sm-4">
                                <BootstrapInput IsTrim @ref="VendorBarCodeRef"
                                                PlaceHolder="BarCode của NCC"
                                                TValue="String"
                                                @onfocusout="@(_ => { StateHasChanged(); })"
                                                @bind-Value="@CurrentItemInfo.VendorBarCode"
                                                IsSelectAllTextOnFocus
                                                ShowLabel="true"
                                                DisplayText="BarCode của NCC"/>
                                <p class="text-danger">
                                </p>
                            </div>
                            <div class="col-12 col-sm-6">
                                <BootstrapInputNumber @ref="LengthRef" PlaceHolder="Độ dài"
                                                      @bind-Value="@CurrentItemInfo.Length"
                                                      IsSelectAllTextOnFocus ShowLabel="true" DisplayText="Độ dài (mm)"/>
                                <p class="text-danger">
                                </p>
                            </div>
                            <div class="col-12 col-sm-6">
                                <BootstrapInputNumber Min="0" PlaceHolder="Độ rộng" @bind-Value="@CurrentItemInfo.Width"
                                                      IsSelectAllTextOnFocus ShowLabel="true" DisplayText="Độ rộng (mm)"/>
                                <p class="text-danger">
                                </p>
                            </div>
                            <div class="col-12 col-sm-6">
                                <BootstrapInputNumber Min="0" PlaceHolder="Độ cao" @bind-Value="@CurrentItemInfo.Height"
                                                      IsSelectAllTextOnFocus ShowLabel="true" DisplayText="Độ cao (mm)"/>
                                <p class="text-danger">
                                </p>
                            </div>
                            <div class="col-12 col-sm-6">
                                <BootstrapInputNumber Min="0" PlaceHolder="Trọng lượng" @bind-Value="@CurrentItemInfo.Weight"
                                                      IsSelectAllTextOnFocus ShowLabel="true" DisplayText="Trọng lượng (mg)"/>
                                <p class="text-danger">
                                </p>
                            </div>
                            <div class="d-flex justify-content-between">
                                <ButtonUpload TValue="String" Accept="image/png, image/gif, image/jpeg" Max="2 * 1024 * 1024"
                                              BrowserButtonText="Tải ảnh lên" IsMultiple="true" ShowProgress="true" BrowserButtonClass="w-100"
                                              OnChange="@OnClickToUpload" OnDelete="@(async fileName => await RemoveImagePreview(fileName))"></ButtonUpload>
                                <div>
                                    <Button IsAsync ButtonStyle="ButtonStyle.None" ButtonType="ButtonType.Submit" Color="Color.Success"
                                            Icon="fa-solid fa-circle-plus"> Thêm
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </ValidateForm>
                </div>
                <div class="col-sm-7">
                    <div class="text-center">
                        <b>Danh sách sản phẩm đã tồn tại</b>
                    </div>
                    <Table ShowLineNo LineNoText="#" TItem="DetailScanProductDto" Items="ListProductInfo">
                        <TableColumns>
                            <TableColumn @bind-Field="@context.ItemCode"/>
                            <TableColumn @bind-Field="@context.BarCode"/>
                            <TableColumn @bind-Field="@context.UnitOfMeasure"/>
                            <TableColumn @bind-Field="@context.Length"/>
                            <TableColumn @bind-Field="@context.Width"/>
                            <TableColumn @bind-Field="@context.Height"/>
                            <TableColumn @bind-Field="@context.Weight"/>
                            <TableColumn @bind-Field="@context.ProductId" Text="Hình ảnh">
                                <Template Context="value">
                                    <BootstrapBlazorIcon @onclick="@(() => OnClickToPreviewImg(value.Row.ProductId))"
                                                         Name="fa-solid fa-image fs-4 text-primary"></BootstrapBlazorIcon>
                                </Template>
                            </TableColumn>
                        </TableColumns>
                    </Table>
                </div>
            </div>
            <div class="text-center "><b>Xem trước hình ảnh tải lên</b></div>
            <div style="max-height:450px" class="overflow-auto">
                @if (ImagesData.Any())
                {
                    <div class="row">
                        @foreach (var image in ImagesData)
                        {
                            <div class="col-sm-6">
                                <img src="@image.Base64" class="border border-1 m-1" style="max-width: 100%; max-height: 100%" alt="sample"/>
                            </div>
                        }
                    </div>
                }
            </div>
        </div>
    </div>
    <Modal @ref="PreviewImageModal" IsBackdrop="true">
        <ModalDialog FullScreenSize="FullScreenSize.ExtraLarge" ShowCloseButton="false" Title="Hình ảnh hàng hóa đã được quét">
            <BodyTemplate>
                @if (ListImageToPreview.Any())
                {
                    foreach (var o in ListImageToPreview)
                    {
                        <img src="@o" class="border border-2 p-2" alt=""/>
                    }
                }
                else
                {
                    <div class="text-center">
                        <b class="py-4">
                            Không có ảnh
                        </b>
                    </div>
                }
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}