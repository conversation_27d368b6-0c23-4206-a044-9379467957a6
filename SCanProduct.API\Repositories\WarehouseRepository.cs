using Microsoft.EntityFrameworkCore;
using SCanProduct.API.Data;
using SCanProduct.API.Repositories.Impl;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
namespace SCanProduct.API.Repositories;

public partial class WarehouseRepository : IWarehouseRepository
{
    private readonly ApplicationDbContext _context;
    public WarehouseRepository(ApplicationDbContext context)
    {
        _context = context;
    }
    public async Task<ApiResponse> ScanningProduct(ScanProductV2Dto scanProductV2Dto)
    {
        var warehouse = await GetOrCreateWarehouse(scanProductV2Dto);
        var itemNumber = GetItemCode().Match(scanProductV2Dto.ScanCode).Value;

        var (existingProduct, locationCode) = await IsProductExistAsync(itemNumber);
        if (existingProduct)
        {
            if (!scanProductV2Dto.SaveAs)
            {
                return new ApiResponse
                {
                    Message = $"SKU đã tồn tại ở vị trí {locationCode}", StatusCode = 400
                };
            }
            await using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                await BlockExistingItems(itemNumber);

                var newProduct = new ScanProductV2
                {
                    Block = false,
                    CreateAt = DateTime.UtcNow.AddHours(7),
                    CreateBy = scanProductV2Dto.CreateBy,
                    ItemCode = itemNumber,
                    WarehouseId = warehouse.WarehouseId
                };
                _context.ScanProductV2S.Add(newProduct);
                await _context.SaveChangesAsync();
                await transaction.CommitAsync();
                return new ApiResponse
                {
                    Message = $"Đã cập nhật vị trí SKU đến {warehouse.LocationCode}", StatusCode = 204
                };
            }
            catch (Exception e)
            {
                await transaction.RollbackAsync();
                return new ApiResponse(500, e.Message);
            }
        }

        //var itemCode = await GetItemCode(itemNumber, scanProductV2Dto.ScanCode);
        // if (itemCode is null)
        // {
        //     return new ApiResponse
        //     {
        //         Message = "BarCode không hợp lệ", StatusCode = 404
        //     };
        // }
        var scanProduct = new ScanProductV2
        {
            Block = false,
            CreateAt = DateTime.UtcNow.AddHours(7),
            CreateBy = scanProductV2Dto.CreateBy,
            ItemCode = "",
            WarehouseId = warehouse.WarehouseId
        };

        _context.ScanProductV2S.Add(scanProduct);
        await _context.SaveChangesAsync();
        return new ApiResponse
        {
            Message = "SKUs  thêm mới thành công", StatusCode = 201
        };

    }
    public async Task<object> GetProducts(int pageNumber = 1, int pageSize = 10, string itemNumber = "", string itemName = "",
        string locationCode = "")
    {
        pageNumber = pageNumber < 1 ? 1 : pageNumber;
        pageSize = pageSize < 1 ? 10 : pageSize;
        var query = _context.ScanProductV2S
            .AsNoTracking()
            .Include(h => h.Warehouse)
            .Include(x => x.ItemCodeNavigation)
            .Where(h => !h.Block)
            .OrderByDescending(x => x.CreateAt)
            .AsQueryable();

        if (!string.IsNullOrEmpty(itemNumber))
        {
            query = query.Where(p => p.ItemCode == itemNumber);
        }
        if (!string.IsNullOrEmpty(itemName))
        {
            query = query.Where(p => p.ItemCodeNavigation.Name!.ToLower().Contains(itemName.ToLower()));
        }

        if (!string.IsNullOrEmpty(locationCode))
        {
            query = query.Where(p => p.Warehouse.LocationCode == locationCode);
        }

        var groupedQuery = query
            .GroupBy(p => new
            {
                p.Warehouse.LocationCode, p.Warehouse.Location
            })
            .Select(g => new
            {
                g.Key.LocationCode,
                g.Key.Location,
                Products = g.OrderByDescending(x => x.CreateAt).Select(p => new
                {
                    p.ItemCode, ItemName = p.ItemCodeNavigation.Name, p.CreateBy, p.CreateAt
                })
            });

        var totalGroups = await groupedQuery.CountAsync();

        var paginatedData = await groupedQuery
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        var totalPages = (int)Math.Ceiling((double)totalGroups / pageSize);

        var result = new
        {
            TotalCount = totalGroups,
            PageSize = pageSize,
            CurrentPage = pageNumber,
            TotalPages = totalPages,
            Data = paginatedData
        };
        return result;
    }
}
