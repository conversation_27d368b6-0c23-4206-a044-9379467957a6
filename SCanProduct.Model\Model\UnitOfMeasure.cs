﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace SCanProduct.Model.Model;

[Keyless]
[Table("Unit of Measure")]
public class UnitOfMeasure
{
    [StringLength(100)] public string Code { get; set; } = null!;

    [StringLength(200)] public string Description { get; set; } = null!;

    [Column("RowID")] public int RowId { get; set; }

    public int? Status { get; set; }
}