@page "/login"
@using SCanProduct.UI.Components.Layout
@layout AuthLayout
<section class="d-flex flex-column justify-content-center " style="height: 100vh;">
    <div class="container-fluid h-custom">
        <div class="row d-flex justify-content-center align-items-center h-100">
            <div class="col-md-9 col-lg-6 col-xl-5 ">
                <img src="https://mdbcdn.b-cdn.net/img/Photos/new-templates/bootstrap-login-form/draw2.webp"
                     class="img-fluid" alt="Sample image">
            </div>
            <div class="col-md-8 col-lg-6 col-xl-4 offset-xl-1 ">
                <EditForm Model="_loginModel" OnValidSubmit="SignIn">
                    <DataAnnotationsValidator/>
                    <ValidationSummary/>
                    <div class="d-flex flex-row align-items-center mb-4 justify-content-center justify-content-lg-start">
                        <h1 class="  mb-0 me-3">Đăng nhập</h1>
                    </div>
                    <div class="form-outline mb-4">
                        <label class="form-label" for="email">
                            Tên đăng nhập <span class="text-danger ">*</span>
                        </label>
                        <InputText Id="email" class="form-control form-control-lg" @bind-Value="@_loginModel.Username"/>
                        <ValidationMessage For="@(() => _loginModel.Username)"/>
                    </div>
                    <div class="form-outline mb-3">
                        <label class="form-label" for="password">
                            Mật khẩu <span class="text-danger ">*</span>
                        </label>
                        <InputText Type="password" Id="password" class="form-control form-control-lg" @bind-Value="@_loginModel.Password"/>
                        <ValidationMessage For="@(() => _loginModel.Password)"/>
                    </div>
                    <div class="text-danger  text-center">@_msg</div>
                    <div class="text-center text-lg-start mt-4 pt-2">
                        <button type="submit" class="btn btn-primary w-100 btn-lg"
                                style="padding-left: 2.5rem; padding-right: 2.5rem;">
                            Login
                        </button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</section>