using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace SCanProduct.Model.Extensions;

public static class CustomEnumExtensions
{
    public static Dictionary<string, string> GetDisplayNamesWithValues<TEnum>() where TEnum : Enum
    {
        var type = typeof(TEnum);
        var result = Enum.GetValues(type)
                         .Cast<Enum>()
                         .ToDictionary(
                             value =>
                             {
                                 var memberInfo = type.GetMember(value.ToString());
                                 if (memberInfo.Length > 0)
                                 {
                                     var attributes = memberInfo[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                                     if (attributes.Length > 0)
                                     {
                                         return ((DisplayAttribute)attributes[0]).Name ?? value.ToString();
                                     }
                                 }
                                 return value.ToString();
                             },
                             value => Convert.ToInt32(value).ToString() // Convert enum value to its integer representation as a string
                         );
        return result;
    }
    public static (string, int)? GetDisplayNameAndValue<TEnum>(int value) where TEnum : Enum
    {
        var enumType = typeof(TEnum);
        foreach (var enumValue in Enum.GetValues(enumType))
        {
            if ((int)enumValue == value)
            {
                var displayName = enumType
                    .GetMember(enumValue.ToString())
                    .FirstOrDefault()?
                    .GetCustomAttributes<DisplayAttribute>()?
                    .FirstOrDefault()?
                    .Name ?? enumValue.ToString();

                return (displayName, value);
            }
        }

        return null; // Trả về null nếu không khớp
    }
}
