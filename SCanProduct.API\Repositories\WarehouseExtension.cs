﻿using System.Text.RegularExpressions;
using Microsoft.EntityFrameworkCore;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
namespace SCanProduct.API.Repositories;

public partial class WarehouseRepository
{
    private async Task<Warehouse> GetOrCreateWarehouse(ScanProductV2Dto scanProductV2Dto)
    {
        var warehouse = await _context.Warehouses.FirstOrDefaultAsync(x => x.LocationCode == scanProductV2Dto.LocationCode)
                        ?? new Warehouse
                        {
                            LocationCode = scanProductV2Dto.LocationCode,
                            Block = false,
                            CreateAt = DateTime.Now,
                            CreateBy = scanProductV2Dto.CreateBy,
                            Location = scanProductV2Dto.Location
                        };

        if (warehouse.WarehouseId != 0)
        {
            return warehouse;
        }
        _context.Warehouses.Add(warehouse);
        await _context.SaveChangesAsync();
        return warehouse;
    }

    // private async Task<string?> GetItemCode(string itemNumber, string scanCode)
    // {
    //     var item = await _context.Items.FirstOrDefaultAsync(h => h.No == itemNumber);
    //     if (item is not null)
    //     {
    //         return item.No;
    //     }
    //     return await _context.ScanProducts.Where(g => g.BarCode == scanCode).Select(x => x.ItemCode).FirstOrDefaultAsync();
    // }

    private async Task<(bool isDuplicate, string? locationCode)> IsProductExistAsync(string itemCode)
    {
        var product = await _context.ScanProductV2S
            .Where(h => h.ItemCode == itemCode && h.Block == false)
            .Include(x => x.Warehouse)
            .Select(h => new
            {
                h.Warehouse.LocationCode
            })
            .FirstOrDefaultAsync();

        return product != null ?
            (true, product.LocationCode) : (false, null);
    }

    private async Task BlockExistingItems(string itemCode) => await _context.ScanProductV2S
        .Where(x => x.ItemCode == itemCode && !x.Block)
        .ExecuteUpdateAsync(x => x.SetProperty(p => p.Block, true));

    [GeneratedRegex(@"^\d{6}")]
    private static partial Regex GetItemCode();
}
