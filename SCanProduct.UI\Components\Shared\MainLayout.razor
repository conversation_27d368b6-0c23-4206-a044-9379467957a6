﻿@inherits LayoutComponentBase

<BootstrapBlazorRoot>
    <Layout SideWidth="0" IsPage="true" ShowGotoTop="true" ShowCollapseBar="true"
            IsFullSide="@IsFullSide" IsFixedHeader="@IsFixedHeader" IsFixedFooter="@IsFixedFooter" ShowFooter="@ShowFooter"
            TabDefaultUrl="/"
            Menus="@Menus" UseTabSet="@UseTabSet" AdditionalAssemblies="new[] { GetType().Assembly }" class="@Theme">
        <Header>
            <span class="ms-3 flex-sm-fill d-none d-sm-block">Ki<PERSON><PERSON> kê sản phẩm</span>
            <div class="flex-fill d-sm-none">
            </div>

            <Logout ImageUrl="images/logo-trungson.jpg" DisplayName="Admin" UserName="Admin">
                <LinkTemplate>
                    <LogoutLink Url="/auth/logout"/>
                </LinkTemplate>
            </Logout>
            <div class="layout-drawer" @onclick="@(e => IsOpen = !IsOpen)"><i class="fa fa-gears"></i></div>
        </Header>
        <Side>
            <div class="layout-banner">
                <img class="layout-logo" src="/images/logo-trungson.jpg"/>
                <div class="layout-title">
                    <span>Trung Son</span>
                </div>
            </div>
        </Side>
        <Main>
            <CascadingValue Value="this" IsFixed="true">
                @Body
            </CascadingValue>
        </Main>
        <Footer>
            <div class="text-center flex-fill">
                <a class="page-layout-demo-footer-link" href="/" target="_blank">Trung Son's Developers make with ❤</a>
            </div>
        </Footer>
        <NotFound>
            <p>Sorry, there's nothing at this address.</p>
        </NotFound>
    </Layout>

    <Drawer Placement="Placement.Right" @bind-IsOpen="@IsOpen" IsBackdrop="true">
        <div class="layout-drawer-body">
            <div class="btn btn-info w-100" @onclick="@(e => IsOpen = false)">Đóng</div>
            <GroupBox Title="Điều chỉnh bố cục">
                <div class="row">
                    <div class="col-6">
                        <div class="layout-item @(IsFullSide ? "active d-flex" : "d-flex")" @onclick="@(e => IsFullSide = true)" data-toggle="tooltip"
                             title="左右结构">
                            <div class="layout-left d-flex flex-column">
                                <div class="layout-left-header"></div>
                                <div class="layout-left-body flex-fill"></div>
                            </div>
                            <div class="layout-right d-flex flex-column flex-fill">
                                <div class="layout-right-header"></div>
                                <div class="layout-right-body flex-fill"></div>
                                <div class="layout-right-footer"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="layout-item flex-column @(IsFullSide ? "d-flex" : "active d-flex")" @onclick="@(e => IsFullSide = false)"
                             data-toggle="tooltip" title="上下结构">
                            <div class="layout-top">
                            </div>
                            <div class="layout-body d-flex flex-fill">
                                <div class="layout-left">
                                </div>
                                <div class="layout-right flex-fill">
                                </div>
                            </div>
                            <div class="layout-right-footer">
                            </div>
                        </div>
                    </div>
                </div>
            </GroupBox>

            <GroupBox Title="Điều chỉnh cố định">
                <div class="row">
                    <div class="col-6 d-flex align-items-center">
                        <Switch @bind-Value="@IsFixedHeader" OnColor="@Color.Success" OffColor="@Color.Secondary"></Switch>
                    </div>
                    <div class="col-6 text-right">
                        <span class="cell-label">Tiêu đề cố định</span>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-6 d-flex align-items-center">
                        <Switch @bind-Value="@IsFixedFooter" OnColor="@Color.Success" OffColor="@Color.Secondary"></Switch>
                    </div>
                    <div class="col-6 text-right">
                        <span class="cell-label">Footer cố định</span>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-6 d-flex align-items-center">
                        <Switch @bind-Value="@ShowFooter" OnColor="@Color.Success" OffColor="@Color.Primary"></Switch>
                    </div>
                    <div class="col-6 text-right">
                        <span class="cell-label">Hiển thị footer</span>
                    </div>
                </div>
            </GroupBox>

            <GroupBox Title="Thêm cài đặt">
                <div class="row">
                    <div class="col-6 d-flex align-items-center">
                        <Switch @bind-Value="@UseTabSet" OnColor="@Color.Success" OffColor="@Color.Primary"></Switch>
                    </div>
                    <div class="col-6 text-right">
                        <span class="cell-label">@(UseTabSet ? "nhiều thẻ" : "trang đơn")</span>
                    </div>
                </div>
            </GroupBox>
        </div>
    </Drawer>
</BootstrapBlazorRoot>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>
