﻿using System.Diagnostics.CodeAnalysis;
using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using SCanProduct.Model.Dto;
using SCanProduct.UI.Services;
namespace SCanProduct.UI.Components.Pages;

public class ListProductBase : ComponentBase
{
    [Inject]
    private IProductService ProductService { get; set; } = default!;
    [Inject]
    protected ToastService ToastService { get; set; } = default!;
    [Inject] public IJSRuntime JsRuntime { get; set; } = default!;
    private List<DetailScanProductDto> ListProductInfo { get; set; } = [];
    protected DetailScanProductDto CurrentProductInfo { get; set; } = new DetailScanProductDto();
    protected List<string> ListImageToPreview { get; set; } = [];
    protected bool IsLoading { get; set; }
    protected DetailScanProductDto? SearchModel
    {
        get;
    } = new DetailScanProductDto();
    private int PageNumber { get; set; } = 1;
    protected int PageSize { get; set; } = 25;
    protected int TotalCount { get; set; }
    [NotNull]
    protected Modal? PreviewImageModal { get; set; }
    [NotNull]
    protected Modal? ConfirmBlockProductModal { get; set; }
    protected static IEnumerable<int> PageItemsSource =>
    [
        25, 50, 100, 1000
    ];
    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        //await LoadListProduct();
        await base.OnInitializedAsync();
        IsLoading = false;
    }
    protected async Task OnExportExcel()
    {
        var resp = await ProductService.ExportAllProductToExcel();
        if (resp is not null)
        {
            var now = DateTime.UtcNow.AddHours(7);
            var fileName = "Danh sach SKUs " + now.ToString("dd-MM-yyyy") + "_" + now.ToString("HH:mm:ss") + ".xlsx";
            await JsRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, resp.ToString());
        }
        else
        {
            await ToastService.Error("Hệ thống", "Không thể xuất file ngay lúc này. Thử lại sau.");
        }
    }
    protected async Task LoadListProduct()
    {
        var resp = await ProductService.GetListProducts(PageNumber, PageSize);
        ListProductInfo = resp.TotalCount > 0 ? resp.Products : new List<DetailScanProductDto>();
    }
    protected void OnClickToPreviewImg(int productId)
    {
        ListImageToPreview = ListProductInfo.Find(x => x.ProductId == productId)!.Images.Select(x => x.Url).ToList()!;
        PreviewImageModal.Toggle();
    }
    protected void OnClickToBlockProduct(int productId)
    {
        CurrentProductInfo = ListProductInfo.Find(x => x.ProductId == productId)!;
        ConfirmBlockProductModal.Toggle();
    }
    protected async Task<QueryData<DetailScanProductDto>> OnQueryAsync(QueryPageOptions options)
    {
        PageNumber = options.PageIndex;
        PageSize = options.PageItems;
        var itemNumber = options.SearchText;
        var resp = await ProductService.GetListProducts(PageNumber, PageSize, itemNumber);
        ListProductInfo = resp.TotalCount > 0 ? resp.Products : new List<DetailScanProductDto>();
        var items = ListProductInfo;
        TotalCount = resp.TotalCount;
        var utcPlus7TimeZone = TimeZoneInfo.FindSystemTimeZoneById("SE Asia Standard Time");// UTC+7
        foreach (var item in ListProductInfo)
        {
            item.CreateDate = TimeZoneInfo.ConvertTimeFromUtc(item.CreateDate, utcPlus7TimeZone);
        }
        return new QueryData<DetailScanProductDto>
        {
            Items = items, TotalCount = TotalCount
        };
    }
    protected async Task<bool> OnSaveAsync()
    {
        var resp = await ProductService.BlockProduct(CurrentProductInfo.ProductId);
        if (resp)
        {
            await LoadListProduct();
            await ConfirmBlockProductModal.Toggle();
            await ToastService.Information("Hệ thống", "Xóa sản phẩm thành công!");
        }
        else
        {
            await ToastService.Information("Hệ thống", "Xóa sản phẩm thất bại, vui lòng thử lại sau!");
        }
        return resp;
    }
}
