using System.Text;
using Blazored.LocalStorage;
using BootstrapBlazor.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Options;
using SCanProduct.UI.Authentication;
using SCanProduct.UI.Components;
using SCanProduct.UI.Services;
namespace SCanProduct.UI;

public class Program
{
    public static HttpClient HttpClient { get; private set; } = default!;

    public static IConfiguration Configuration { get; private set; } = default!;
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        Configuration = builder.Configuration;

        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

        builder.Services.AddScoped<AuthenticationStateProvider, ApiAuthenticationStateProvider>();
        builder.Services.AddRazorComponents().AddInteractiveServerComponents();
        builder.Services.AddSingleton<IProductService, ProductService>();
        builder.Services.AddScoped<IAuthService, AuthService>();
        builder.Services.AddHttpContextAccessor();

        builder.Services.AddBootstrapBlazor(options =>
        {
            options.ToastDelay = 4000;
            options.ToastPlacement = Placement.TopEnd;
        });

        builder.Logging.AddConsole();

        var apiUrl = Configuration.GetSection("API_URL").Value ?? "https://scan.api:8080";
        HttpClient = new HttpClient
        {
            BaseAddress = new Uri(apiUrl)
        };
        builder.Services.AddBlazoredLocalStorage();

        builder.Services.Configure<HubOptions>(option => option.MaximumReceiveMessageSize = null);
        builder.Services.AddRazorPages();
        builder.Services.AddServerSideBlazor();
        var app = builder.Build();

        if (!app.Environment.IsDevelopment())
        {
            app.UseExceptionHandler("/Error");
            app.UseResponseCompression();
        }
        var option = app.Services.GetService<IOptions<RequestLocalizationOptions>>();
        if (option != null)
        {
            app.UseRequestLocalization(option.Value);
        }
        app.UseHttpsRedirection();
        app.UseStaticFiles();
        app.UseAntiforgery();
        app.MapRazorComponents<App>().AddInteractiveServerRenderMode();
        app.Run();
    }
}
