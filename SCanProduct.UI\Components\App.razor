﻿@inject IHostEnvironment Env
@inject IStringLocalizer<App> Localizer

<!DOCTYPE html>
<html lang="en" data-bs-theme='light'>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="keywords" content="">
    <meta name="description" content="Trung Sơn - SCan Product System">
    <meta name="author" content="Trịnh Trung - Hoàng Hiếu - Hoài Nam">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="favicon.png">
    <base href="/" />
    <Link Href="_content/BootstrapBlazor.FontAwesome/css/font-awesome.min.css" />
    <Link Href="_content/BootstrapBlazor.MaterialDesign/css/md.min.css" />
    <Link Href="_content/BootstrapBlazor/css/bootstrap.blazor.bundle.min.css" />
    <Link Href="_content/BootstrapBlazor/css/motronic.min.css" />
    <Link Href="SCanProduct.UI.styles.css" />
    <Link Href="css/app.css" />
    <title>Trung Son Pharma</title>
    <HeadOutlet @rendermode="new InteractiveServerRenderMode(false)" />
</head>

<body>
    <Routes @rendermode="new InteractiveServerRenderMode(false)" />

    <ReconnectorOutlet ReconnectInterval="5000" @rendermode="new InteractiveServerRenderMode(false)" />

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <Script Src="_content/BootstrapBlazor/js/bootstrap.blazor.bundle.min.js"></Script>
    <script src="_framework/blazor.web.js"></script>
    <script src="js/app.js"></script>
    <script src="js/three-renderer.js"></script>
</body>

</html>
