﻿@page "/warehouse"
@using SCanProduct.UI.Models
@inject IJSRuntime Js
@if (IsLoading)
{
    <p>Loading...</p>
}
else
{
    <div class="card" style="min-height:83vh">
        <div class="card-body">
            @if (ShowScanLocationCode)
            {
                <ZXingBlazor.Components.BarcodeReader ScanResult="@(async code => await ScanResultLocation(code))"
                                                      Close="()=>ShowScanLocationCode=!ShowScanLocationCode"
                                                      ScanBtnTitle="Quét"
                                                      ResetBtnTitle="Reset"
                                                      SelectDeviceBtnTitle="Chọn thiết bị"
                                                      CloseBtnTitle="Đóng"/>
            }
            else if (ShowScanItemCode)
            {
                <ZXingBlazor.Components.BarcodeReader ScanResult="@(async code => await ScanResultItemCode(code))"
                                                      Close="()=>ShowScanItemCode=!ShowScanItemCode"
                                                      ScanBtnTitle="Quét"
                                                      SelectDeviceBtnTitle="Chọn thiết bị"
                                                      ResetBtnTitle="Reset"
                                                      CloseBtnTitle="Đóng"/>
            }
            <Segmented IsBlock="true" ShowTooltip class="w-100 mb-2" Items="@SegmentedLocations" Value="@SelectedLocation"
                       OnValueChanged="OnLocationValueChanged">
                <ItemTemplate>
                    <span>
                        <b>@context.Text</b>
                    </span>
                </ItemTemplate>
            </Segmented>
            <ValidateForm @ref="ValidateFormRef" Model="@CurrentItemInfo" Id="@ValidateFormId"
                          OnValidSubmit="@(_ => OnAddOrUpdateProduct())">
                <DataAnnotationsValidator/>
                <div class="row">
                    <div class="col-2 mt-4 pt-1 p-0 d-flex justify-content-center">
                        <Button IsAsync ButtonStyle="ButtonStyle.None" OnClick="@(_ => ShowScanLocationCode = true)"
                                Color="Color.Success" Icon="fa-solid fa-qrcode">
                        </Button>
                    </div>
                    <div class="col-8 px-0">
                        <BootstrapInput IsTrim PlaceHolder="Location Code" TValue="String" @ref="LocationCodeRef"
                                        OnEnterAsync="@(async _ =>
                                                      {
                                                          LocationToSearch = LocationCodeRef.Value;
                                                          await TableProductRef.QueryAsync();
                                                          await ItemNoRef.FocusAsync();
                                                      })"
                                        @onfocus="@(_ =>
                                                  {
                                                      CurrentItemInfo.ItemNo = string.Empty;
                                                  })"
                                        OnBlurAsync="@(async _ =>
                                                     {
                                                         LocationToSearch = LocationCodeRef.Value;
                                                         await TableProductRef.QueryAsync();
                                                         await ItemNoRef.FocusAsync();
                                                     })"
                                        @bind-Value="@CurrentItemInfo.LocationCode" IsSelectAllTextOnFocus
                                        ShowLabel="true"
                                        IsAutoFocus="true"
                                        DisplayText="Location Code"/>
                    </div>
                    <div class="col-2 mt-4 pt-1 p-0 d-flex justify-content-center">
                        <Button IsAsync ButtonStyle="ButtonStyle.None" OnClick="@(_ => ShowScanLocationCode = true)"
                                Color="Color.Success" Icon="fa-solid fa-qrcode">
                        </Button>
                    </div>
                </div>
                <div class="row">
                    <div class=" col-2 mt-4 pt-1 p-0 d-flex justify-content-center">
                        <Button IsAsync ButtonStyle="ButtonStyle.None"
                                IsDisabled="@(string.IsNullOrEmpty(CurrentItemInfo.LocationCode))"
                                OnClick="@(_ => ShowScanItemCode = true)"
                                Color="Color.Success" Icon="fa-solid fa-qrcode">
                        </Button>
                    </div>
                    <div class="col-8 p-0">
                        <BootstrapInput TValue="String" @ref="ItemNoRef"
                                        IsDisabled="@(string.IsNullOrEmpty(CurrentItemInfo.LocationCode))" IsTrim
                                        OnEnterAsync="@(async _ =>
                                                      {
                                                          await SubmitButtonRef.FocusAsync();
                                                      })"
                                        PlaceHolder="Mã SKU" ShowLabel="true"
                                        @bind-Value="@CurrentItemInfo.ItemNo" IsSelectAllTextOnFocus
                                        DisplayText="Barcode/QRCode"/>
                    </div>
                    <div class=" col-2 mt-4 pt-1 p-0 d-flex justify-content-center">
                        <Button IsAsync ButtonStyle="ButtonStyle.None"
                                IsDisabled="@(string.IsNullOrEmpty(CurrentItemInfo.LocationCode))"
                                OnClick="@(_ => ShowScanItemCode = true)"
                                Color="Color.Success" Icon="fa-solid fa-qrcode">
                        </Button>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="@(IsUpdate ? "col-6" : "col-12")">
                        <div class="d-flex flex-column justify-content-center">
                            <Button @ref="SubmitButtonRef" IsAsync ButtonStyle="ButtonStyle.None"
                                    ButtonType="ButtonType.Submit"
                                    IsDisabled="string.IsNullOrEmpty(CurrentItemInfo.ItemNo)"
                                    Color="@(string.IsNullOrEmpty(CurrentItemInfo.ItemNo) ? Color.Secondary : Color.Success)"
                                    Icon="@("fa-solid " + (IsUpdate ? "fa-pen-to-square" : "fa-circle-plus"))">
                                <span class="mx-2">
                                    @(IsUpdate ? "Cập nhật" : $"Thêm mới {(string.IsNullOrEmpty(SelectedLocationText) ? "" : " vào ")}") <b>@SelectedLocationText</b>
                                </span>
                            </Button>
                        </div>
                    </div>
                    @if (IsUpdate)
                    {
                        <div class="@(IsUpdate ? "col-6" : "col-12")">
                            <div class="d-flex flex-column justify-content-center">
                                <Button OnClick="@(async _ =>
                                                 {
                                                     IsUpdate = false;
                                                     await ItemNoRef.FocusAsync();
                                                 })" ButtonStyle="ButtonStyle.None"
                                        Color="Color.Danger">
                                    Hủy
                                </Button>
                            </div>
                        </div>
                    }
                </div>

            </ValidateForm>
            <div class="text-center mt-3">
                <b>Danh sách sản phẩm đã được quét tại @(string.IsNullOrEmpty(CurrentItemInfo.LocationCode) ? "tất cả " : "")vị trí
                    @if (!string.IsNullOrEmpty(CurrentItemInfo.LocationCode))
                    {
                        <Badge Color="Color.Success">@CurrentItemInfo.LocationCode</Badge>
                    }
                </b>
            </div>
            <div class="">
                <div class="mb-3">
                    <div class="row">
                        <div class="col-lg-4 col-sm-12 mb-1">
                            <div class="row">
                                <div class="col-10">
                                    <BootstrapInput PlaceHolder="Search Location"
                                                    TValue="String"
                                                    @ref="SearchLocationCodeRef"
                                                    Value="@CurrentItemInfo.LocationCode"
                                                    ValueChanged="@OnSearchLocationCodeChanged"
                                                    IsSelectAllTextOnFocus="true"
                                                    ShowLabel="true"
                                                    DisplayText="Search Location"/>
                                </div>
                                <div class="col-2 px-0 d-flex justify-content-center">
                                    <div class="mt-4 pt-1">
                                        <Button IsAsync ButtonStyle="ButtonStyle.None"
                                                OnClick="@(async _ =>
                                                         {
                                                             await OnSearchLocationCodeChanged("");
                                                             CurrentItemInfo.LocationCode = string.Empty;
                                                             await SearchLocationCodeRef.FocusAsync();
                                                         })"
                                                Color="Color.Danger" Icon="fa-solid fa-delete-left">
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12 mb-1">
                            <div class="row">
                                <div class="col-10">
                                    <BootstrapInput PlaceHolder="Search Item code"
                                                    TValue="String"
                                                    @ref="SearchItemCodeRef"
                                                    Value="@ItemNumberToSearch"
                                                    ValueChanged="@OnSearchItemCodeChanged"
                                                    IsSelectAllTextOnFocus="true"
                                                    ShowLabel="true"
                                                    DisplayText="Search Item code"/>
                                </div>
                                <div class="col-2 px-0 d-flex justify-content-center">
                                    <div class="mt-4 pt-1">
                                        <Button IsAsync ButtonStyle="ButtonStyle.None"
                                                OnClick="@(async _ =>
                                                         {
                                                             await OnSearchItemCodeChanged("");
                                                             await SearchItemCodeRef.FocusAsync();
                                                         })"
                                                Color="Color.Danger" Icon="fa-solid fa-delete-left">
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-sm-12 mb-1">
                            <div class="row">
                                <div class="col-10">
                                    <BootstrapInput PlaceHolder="Search Item name"
                                                    TValue="String"
                                                    @ref="SearchItemNameRef"
                                                    Value="@ItemNameToSearch"
                                                    ValueChanged="@OnSearchItemNameChanged"
                                                    IsSelectAllTextOnFocus="true"
                                                    ShowLabel="true"
                                                    DisplayText="Search Item name"/>
                                </div>
                                <div class="col-2 px-0 d-flex justify-content-center">
                                    <div class="mt-4 pt-1">
                                        <Button IsAsync ButtonStyle="ButtonStyle.None"
                                                OnClick="@(async _ =>
                                                         {
                                                             await OnSearchItemNameChanged("");
                                                             await SearchItemNameRef.FocusAsync();
                                                         })"
                                                Color="Color.Danger" Icon="fa-solid fa-delete-left">
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <Divider Text="Chú thích"/>
                <div class="d-flex justify-content-around mb-2">
                    <Badge Color="Color.Primary">
                        Kho lẻ
                    </Badge>
                    <Badge Color="Color.Warning">
                        Kho chẵn
                    </Badge>
                </div>
                <Table ShowLineNo @ref="TableProductRef"
                       LineNoText="#"
                       IsFixedHeader="true"
                       IsFixedFooter="true"
                       ShowGotoNavigator="false"
                       IsPagination="true"
                       PageItemsSource="@PageItemsSource"
                       ShowLoading="true" ShowPageInfo="true"
                       OnQueryAsync="@OnQueryAsync"
                       ShowRefresh="true"
                       Height="800" TItem="WarehouseProductDto">
                    <TableColumns>
                        <TableColumn @bind-Field="@context.LocationCode" Text="Location Code" Width="250">
                            <Template Context="value">
                                @value.Row.LocationCode
                                <Badge Color="@(value.Row.Location == 0 ? Color.Primary : Color.Warning)" class="ms-3">
                                    @value.Row.Products.Count() item(s)
                                </Badge>
                            </Template>
                        </TableColumn>
                        <TableColumn @bind-Field="@context.Products">
                            <Template Context="value">
                                <div class="row w-100 g-2">
                                    @foreach (var item in value.Row.Products)
                                    {
                                        <Tag Color="Color.Light">
                                            @* <div class="p-2 ps-2 border-1 border bg-white rounded-2"> *@
                                            <ul class="mb-0">
                                                <li>
                                                    Code:
                                                    <b>
                                                        @item.ItemCode
                                                    </b>
                                                </li>
                                                <li><b><em>@item.ItemName</em></b></li>
                                                <li>@if (item.CreateBy.ToLower().Equals("web"))
                                                    {
                                                        <i class="fa-solid fa-laptop-code"></i>
                                                    }
                                                    else
                                                    {
                                                        <i class="fa-solid fa-mobile-screen-button"></i>
                                                    }
                                                    ●<span class="mx-1"><em>@item.CreateAt.ToString("dd-MM-yy HH:mm:ss")</em></span>
                                                </li>
                                            </ul>
                                            @* </div> *@
                                        </Tag>
                                    }
                                </div>
                            </Template>
                        </TableColumn>
                    </TableColumns>
                </Table>
            </div>
        </div>
    </div>
    <Modal @ref="Modal" IsBackdrop="true" Size="Size.Large" OnCloseAsync="@(async () => { await ItemNoRef.FocusAsync(); })" IsKeyboard="true">
        <ModalDialog Title="Thông báo!" IsCentered ShowCloseButton>
            <BodyTemplate>
                <Alert Color="Color.Info">
                    <b>@RespMsg</b>
                </Alert>
            </BodyTemplate>
        </ModalDialog>
    </Modal>
}