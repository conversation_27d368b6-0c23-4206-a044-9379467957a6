using AutoMapper;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
namespace SCanProduct.API.Decorators;

public class MappingProfile : Profile
{
    public MappingProfile()
    {
        CreateMap<ScanProduct, CreateScanProductDto>().ReverseMap();
        CreateMap<ScanProduct, DetailScanProductDto>().ReverseMap();

        CreateMap<Image, DetailImageDto>().ReverseMap();
        CreateMap<Warehouse, WarehouseDto>().ReverseMap();

        CreateMap<ScanProduct, DetailScanProductDto>()
            .ForMember(destinationMember: dest => dest.ItemName, memberOptions: opt => opt.MapFrom(src => src.ItemCodeNavigation.Name))
            .ReverseMap();

        CreateMap<ScanProduct, ScanProductDto>()
            .ForMember(destinationMember: dest => dest.ItemName, memberOptions: opt => opt.MapFrom(src => src.ItemCodeNavigation.Name))
            .ReverseMap();
    }
}
