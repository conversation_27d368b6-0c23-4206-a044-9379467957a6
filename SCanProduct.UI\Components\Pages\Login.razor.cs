﻿using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using SCanProduct.Model.Dto;
using SCanProduct.UI.Services;
namespace SCanProduct.UI.Components.Pages;

public partial class Login
{

    private LoginDto _loginModel = new LoginDto
    {
        Username = "", Password = ""
    };
    private string? _msg;
    [Inject]
    private ILocalStorageService? LocalStorage { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; } = default!;
    [Inject]
    protected IAuthService AuthService { get; set; } = default!;

    [Inject] private AuthenticationStateProvider AuthenticationStateProvider { get; set; } = default!;
    private async Task SignIn()
    {
        var login = await AuthService.LoginAsync(_loginModel);
        if (!login.Success)
        {
            _msg = "Đăng nhập thất bại, sai tên đăng nhập hoặc mật khẩu.";
            return;
        }
        _msg = "Đăng nhập thành công.";
        await LocalStorage!.SetItemAsync("userName", _loginModel.Username.ToUpper());
        NavigationManager.NavigateTo("/", true);
    }
}
