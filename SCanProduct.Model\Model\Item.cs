﻿namespace SCanProduct.Model.Model;

public class Item
{
    public string No { get; set; } = null!;

    public string? No2 { get; set; }

    public string? Name { get; set; }

    public string? Description { get; set; }

    public string? Description2 { get; set; }

    public string? Class { get; set; }

    public string? BaseUnitOfMeasure { get; set; }

    public int? PriceUnitConversion { get; set; }

    public string? InventoryPostingGroup { get; set; }

    public string? ItemCategoryCode { get; set; }

    public string? ProductGroupCode { get; set; }

    public string? SubGroupCode { get; set; }

    public string? VatProdPostingGroup { get; set; }

    public string? ItemDiscGroup { get; set; }

    public string? ManufacturerCode { get; set; }

    public string? CountryOfOriginCode { get; set; }

    public string? CountryPurchasedCode { get; set; }

    public string? ItemGroup1 { get; set; }

    public string? ItemGroup2 { get; set; }

    public string? ItemGroup3 { get; set; }

    public string? ItemGroup4 { get; set; }

    public string? ItemGroup5 { get; set; }

    public string? SalaryGroup { get; set; }

    public string? TaxGroupCode { get; set; }

    public string? CommissionGroup { get; set; }

    public int? StatisticsGroup { get; set; }

    public decimal? LotSize { get; set; }

    public string? CommonItemNo { get; set; }

    public string? ShelfNo { get; set; }

    public int? AllowInvoiceDisc { get; set; }

    public int? PriceProfitCalculation { get; set; }

    public decimal? Profit { get; set; }

    public int? CostingMethod { get; set; }

    public decimal? UnitPrice { get; set; }

    public decimal? UnitCost { get; set; }

    public decimal? StandardCost { get; set; }

    public decimal? LastDirectCost { get; set; }

    public decimal? IndirectCost { get; set; }

    public string? RoutingNo { get; set; }

    public string? ProductionBomNo { get; set; }

    public string? LeadTimeCalculation { get; set; }

    public decimal? ReorderPoint { get; set; }

    public decimal? MaximumInventory { get; set; }

    public decimal? ReorderQuantity { get; set; }

    public string? AlternativeItemNo { get; set; }

    public decimal? UnitListPrice { get; set; }

    public decimal? DutyDue { get; set; }

    public string? DutyCode { get; set; }

    public decimal? GrossWeight { get; set; }

    public decimal? NetWeight { get; set; }

    public decimal? UnitsPerParcel { get; set; }

    public decimal? UnitVolume { get; set; }

    public string? VendorNo { get; set; }

    public string? VendorItemNo { get; set; }

    public string? Durability { get; set; }

    public string? FreightType { get; set; }

    public string? TariffNo { get; set; }

    public decimal? DutyUnitConversion { get; set; }

    public decimal? BudgetQuantity { get; set; }

    public decimal? BudgetedAmount { get; set; }

    public decimal? BudgetProfit { get; set; }

    public int? Blocked { get; set; }

    public DateTime? LastDateModified { get; set; }

    public int? BomVersionCode { get; set; }

    public int? Reserve { get; set; }

    public int? LowLevelCode { get; set; }

    public DateTime? LastUnitCostCalcDate { get; set; }

    public decimal? Scrap { get; set; }

    public int? InventoryValueZero { get; set; }

    public decimal? WipQuantity { get; set; }

    public decimal? WipCompletion { get; set; }

    public decimal? EquivalantFinishedQuantity { get; set; }

    public decimal? OverheadRate { get; set; }

    public decimal? AllocatedCost { get; set; }

    public decimal? MinimumOrderQuantity { get; set; }

    public decimal? MaximumOrderQuantity { get; set; }

    public decimal? SafetyStockQuantity { get; set; }

    public decimal? OrderMultiple { get; set; }

    public string? SafetyLeadTime { get; set; }

    public int? FlushingMethod { get; set; }

    public int? ReplenishmentSystem { get; set; }

    public string? SalesUnitOfMeasure { get; set; }

    public string? PurchUnitOfMeasure { get; set; }

    public string? ReorderCycle { get; set; }

    public int? ReorderingPolicy { get; set; }

    public int? IncludeInventory { get; set; }

    public int? ManufacturingPolicy { get; set; }

    public int? AccountType { get; set; }

    public string? PictureNo { get; set; }

    public string? ExpirationCalculation { get; set; }

    public string? SpecialEquipmentCode { get; set; }

    public string? Totaling { get; set; }

    public string? PhysInvtCountingPeriodCode { get; set; }

    public DateTime? LastCountingPeriodUpdate { get; set; }

    public string? NextCountingPeriod { get; set; }

    public int? Nonstock { get; set; }

    public int? OrderTrackingPolicy { get; set; }

    public int? Critical { get; set; }

    public string? Picture { get; set; }

    public int? VersionCode { get; set; }

    public string? QuotaNo { get; set; }

    public decimal? QuotaQuantity { get; set; }

    public string? QuotaAddedNo { get; set; }

    public decimal? QuotaAddedQuantity { get; set; }

    public string? ImportLicenseNo { get; set; }

    public decimal? ImportLicenseQuantity { get; set; }

    public int? QuotaRequest { get; set; }

    public string? Name2 { get; set; }

    public string? Name3 { get; set; }

    public string? No3 { get; set; }

    public decimal? ExperimentDuration { get; set; }

    public string? ExperimentBom { get; set; }

    public string? ExperimentRouting { get; set; }

    public string? SubGroupCode1 { get; set; }

    public string? SubGroupCode2 { get; set; }

    public DateOnly? VisaIssuedDate { get; set; }

    public DateOnly? ExpirationDate { get; set; }

    public string? Standard { get; set; }

    public string? VendorAuthorizationNo { get; set; }

    public string? CompanyRegistration { get; set; }

    public string? QualityMeasureCode { get; set; }

    public string? CharacterGroup { get; set; }

    public string? ItemGroup6 { get; set; }

    public string? SearchName { get; set; }

    public string? VisaIssuedNo { get; set; }

    public string? PlaceOfOriginCode { get; set; }

    public string? LoginId { get; set; }

    public string? ProductionStandard { get; set; }

    public int? Packing { get; set; }

    public decimal? UnitPrice1 { get; set; }

    public int? PickMethod { get; set; }

    public int? LotPickMod { get; set; }

    public string? UserControl { get; set; }

    public string? CustomNo { get; set; }

    public DateTime? CustomDate { get; set; }

    public decimal? IngredientWeight { get; set; }

    public decimal? Aging { get; set; }

    public string? LastUserModified { get; set; }

    public string? BinCode { get; set; }

    public int? Check { get; set; }

    public decimal? Diameter { get; set; }

    public string? GenProdPostingGroup { get; set; }

    public decimal? Height { get; set; }

    public decimal? InternalAmount { get; set; }

    public decimal? InternalAmountLcy { get; set; }

    public decimal? Length { get; set; }

    public string? Manager { get; set; }

    public string? OfBom { get; set; }

    public string? OfItem { get; set; }

    public int? ProductionWorkflowVersion { get; set; }

    public string? Qtqc { get; set; }

    public decimal? Surface { get; set; }

    public string? VersionQc { get; set; }

    public decimal? Width { get; set; }

    public decimal? CifPrice { get; set; }

    public string? CifCurrencyCode { get; set; }

    public string? UomCif { get; set; }

    public string? UnitListPriceCurrencyCode { get; set; }

    public string? UomUnitListPrice { get; set; }

    public decimal? WholesalePrice { get; set; }

    public string? WholesalePriceCurrencyCode { get; set; }

    public string? UomWholesalePrice { get; set; }

    public decimal? WholesalePriceIncludedVat { get; set; }

    public string? WholesalePriceIncludedVatCurrencyCode { get; set; }

    public string? UomWholesalePriceIncludedVat { get; set; }

    public decimal? UnitListPriceIncludedVat { get; set; }

    public string? UnitListPriceIncludedVatCurrencyCode { get; set; }

    public string? UomUnitListPriceIncludedVat { get; set; }

    public string? Specification { get; set; }

    public string? Specification2 { get; set; }

    public string? Specification3 { get; set; }

    public string? Specification4 { get; set; }

    public string? Specification5 { get; set; }

    public string? Specification6 { get; set; }

    public int? Status { get; set; }

    public string? ItemGroup7 { get; set; }

    public string? ItemGroup8 { get; set; }

    public string? ItemGroup9 { get; set; }

    public string? ItemGroup10 { get; set; }

    public string? ItemGroup11 { get; set; }

    public string? ItemGroup12 { get; set; }

    public string? ItemGroup13 { get; set; }

    public string? ItemGroup14 { get; set; }

    public string? ItemGroup15 { get; set; }

    public string? ItemGroup16 { get; set; }

    public string? ItemGroup17 { get; set; }

    public string? ItemGroup18 { get; set; }

    public string? ItemGroup19 { get; set; }

    public string? ItemGroup20 { get; set; }

    public string? SymbolTicket { get; set; }

    public DateOnly? OpenDateFrom { get; set; }

    public DateOnly? OpenDateTo { get; set; }

    public decimal? ParValue { get; set; }

    public decimal? ParValueOfConv { get; set; }

    public string? PrizeStructure { get; set; }

    public string? FormOfInvoice { get; set; }

    public string? SeriInvoice { get; set; }

    public string? NumberOfInvFrom { get; set; }

    public string? NumberOfInvTo { get; set; }

    public string? SymbolItem { get; set; }

    public string? ItemSpecification { get; set; }

    public int? Type { get; set; }

    public decimal? MinimumInventory { get; set; }

    public string? TechnicalStandards { get; set; }

    public string? TechnicalDocs { get; set; }

    public decimal? StandardPrice { get; set; }

    public int? QcTest { get; set; }

    public string? No4 { get; set; }

    public string? No5 { get; set; }

    public decimal? Thick { get; set; }

    public decimal? Weight { get; set; }

    public int RowId { get; set; }

    public DateOnly? PostingDate { get; set; }

    public decimal? UnitCostVat { get; set; }

    public int? Field136 { get; set; }

    public int? OnWeb { get; set; }

    public int? WebStatus { get; set; }

    public int? Temp { get; set; }

    public int? PointTe { get; set; }

    public int? StatusJson { get; set; }

    public int? Checkjson { get; set; }

    public string? ShowReport { get; set; }

    public string? ShowNameReport { get; set; }

    public int? OnHaravan { get; set; }

    public int? HaravanStatus { get; set; }

    public int? IdProduct { get; set; }

    public int? IdVarians { get; set; }

    public string? CategoryNo { get; set; }

    public string? CategoryTypeNo { get; set; }

    public string? CategoryGroupNo { get; set; }

    public string? Route { get; set; }

    public string? Contraindications { get; set; }

    public string? Importer { get; set; }

    public string? Visa { get; set; }

    public string? Label { get; set; }

    public string? ItemLocation { get; set; }

    public decimal? UnitPriceRegis { get; set; }

    public string? MainIngredient { get; set; }

    public string? RegistrationNo { get; set; }

    public string? BoxUnitOfMeasure { get; set; }

    public bool Visible { get; set; }

    public decimal? GiaNhapGanNhat { get; set; }

    public bool? Hidden { get; set; }

    public bool? AllowCampaign { get; set; }

    public bool? AllowWallet { get; set; }

    public bool? AllowLineDisc { get; set; }

    public short? OracleSyncStatus { get; set; }

    public string? ItemCategoryAtcCode { get; set; }

    public virtual ICollection<ScanProductV2> ScanProductV2s { get; set; } = new List<ScanProductV2>();

    public virtual ICollection<ScanProduct> ScanProducts { get; set; } = new List<ScanProduct>();
}
