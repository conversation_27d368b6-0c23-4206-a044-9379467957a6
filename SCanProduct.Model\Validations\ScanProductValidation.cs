﻿using FluentValidation;
using SCanProduct.Model.Dto;
namespace SCanProduct.Model.Validations;

public class ScanProductValidation
{
    public class CreateScanProductDtoValidator : AbstractValidator<CreateScanProductDto>
    {
        public CreateScanProductDtoValidator()
        {
            RuleFor(x => x.ItemCode).NotEmpty();
            RuleFor(x => x.UnitOfMeasure).NotEmpty();
            RuleFor(x => x.Width).GreaterThan(0);
            RuleFor(x => x.Height).GreaterThan(0);
            RuleFor(x => x.Length).GreaterThan(0);
            RuleFor(x => x.Weight).GreaterThanOrEqualTo(0);
        }
    }
}
