using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using SCanProduct.API.Data;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.Model.Model;
using Serilog;
namespace SCanProduct.API;

public class FileUploadServices
{
    private const string UserName = "erpteams";
    private const string PassWord = "Since@2024";
    private const string Folder = "ScanProduct";
    private const string UrlApiFilesUpload = "https://it.trungsonpharma.com:8989/uploads.php";
    private readonly ApplicationDbContext _context;
    public FileUploadServices(ApplicationDbContext context)
    {
        _context = context;
    }
    public async Task<ApiResponse> UploadFileToExternalService(IFormFile file, int productId)
    {
        try
        {
            using var client = new HttpClient();
            var authToken = Encoding.ASCII.GetBytes($"{UserName}:{PassWord}");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(authToken));
            var content = new MultipartFormDataContent();
            // Add file to the form data
            try
            {
                var product = await _context.ScanProducts.FindAsync(productId);
                if (product is null)
                {
                    return new ApiResponse
                    {
                        StatusCode = 404, Message = "Product not found"
                    };
                }
                var itemCode = product.ItemCode;
                var unit = product.UnitOfMeasure;
                var fileNameImage = file.FileName;
                var lastDotIndex = fileNameImage.LastIndexOf('.');
                var extension = lastDotIndex >= 0 ? fileNameImage[lastDotIndex..] : string.Empty;
                var fileName = itemCode + "_" + unit + extension;
                var fileStream = file.OpenReadStream();
                var fileContent = new StreamContent(fileStream);
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(file.ContentType);
                content.Add(fileContent, "Files[]", fileName);
                content.Add(new StringContent(Folder), "folder");

            }
            catch (Exception ex)
            {
                return new ApiResponse
                {
                    StatusCode = 500, Message = $"Error reading file '{file.FileName}' : {ex.Message}"
                };
            }
            Log.Information("Start upload file to server");
            var response = await client.PostAsync(UrlApiFilesUpload, content);
            Log.Information("End upload file to server");
            Log.Information(response.ToString());
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var responseData = JsonConvert.DeserializeObject<FileUploadDto.FilesServerResponse>(responseContent);
                var fileName = responseData!.Files.First().Name;
                var lastDotIndex = fileName.LastIndexOf('.');
                var upperBeforeDot = fileName[..lastDotIndex].ToUpper();
                var afterDot = fileName[lastDotIndex..];
                var result = upperBeforeDot + afterDot;
                var image = new Image
                {
                    Name = result,
                    Url = responseData.Files.First().Url,
                    Size = responseData.Files.First().Size.First().ToString(),
                    Type = responseData.Files.First().Type.First(),
                    ProductId = productId
                };
                await _context.Images.AddAsync(image);
                await _context.SaveChangesAsync();

                return new ApiResponse
                {
                    StatusCode = 200, Message = "File and folder uploaded to server successfully", Result = responseData
                };
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return new ApiResponse
                {
                    StatusCode = 500, Message = $"Failed to upload file and folder: {responseContent}"
                };
            }
        }
        catch (Exception e)
        {
            return new ApiResponse
            {
                StatusCode = 500, Message = $"Error occurred: {e.Message}"
            };
        }
    }
}
