﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
namespace SCanProduct.UI.Models;

public class ProductInfo
{
    [Required(ErrorMessage = "Vui lòng quét Barcode của NCC.")]
    [RegularExpression(@"^[\d\W]{11,13}$",
    ErrorMessage = "Trường này phải có độ dài từ 11 đến 13 ký tự và chỉ chứa số hoặc ký tự đặc biệt.")]
    [Length(11, 13, ErrorMessage = "Mã barcode của NCC phải có từ 11 đến 13 chữ số.")]
    [DisplayName("Barcode của NCC")]
    public string VendorBarCode { get; set; } = null!;
    [DisplayName("Mã hàng")]
    [Required(ErrorMessage = "Vui lòng quét mã hãng.")]
    [RegularExpression(@"^9\d{5}$", ErrorMessage = "Mã sản phẩm phải đúng 6 chữ số.")]
    public string ItemNo { get; set; } = null!;
    [Required(ErrorMessage = "Vui lòng chọn Đơn vị.")]
    [DisplayName("Đơn vị")]
    public string UOM { get; set; } = null!;
    [Required(ErrorMessage = "Vui lòng nhập Chiều rộng.")]
    [Range(1, int.MaxValue, ErrorMessage = "Giá trị Chiều rộng không hợp lệ.")]
    [DisplayName("Chiều rộng")]
    public int Width { get; set; }
    [Required(ErrorMessage = "Vui lòng nhập Chiều cao.")]
    [Range(1, int.MaxValue, ErrorMessage = "Giá trị Chiều cao không hợp lệ.")]
    [DisplayName("Chiều cao")]
    public int Height { get; set; }
    [DisplayName("Trọng lượng")]
    public int Weight { get; set; }
    [Required(ErrorMessage = "Vui lòng nhập Chiều dài.")]
    [Range(1, int.MaxValue, ErrorMessage = "Giá trị Chiều dài không hợp lệ.")]
    [DisplayName("Chiều dài")]
    public int Length { get; set; }

    public class ItemInfoDto
    {
        public List<string> ListUnitOfMeasure { get; set; } = null!;
    }
}
