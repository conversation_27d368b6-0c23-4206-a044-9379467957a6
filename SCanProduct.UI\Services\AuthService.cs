using System.Net.Http.Headers;
using Blazored.LocalStorage;
using Microsoft.AspNetCore.Components.Authorization;
using Newtonsoft.Json;
using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.UI.Authentication;
namespace SCanProduct.UI.Services;

public class AuthService : IAuthService
{
    private readonly AuthenticationStateProvider _authenticationStateProvider;
    private readonly ILocalStorageService _localStorage;
    public AuthService(ILocalStorageService localStorage, AuthenticationStateProvider authenticationStateProvider)
    {
        _localStorage = localStorage;
        _authenticationStateProvider = authenticationStateProvider;
    }
    public async Task<LoginResponseDto> LoginAsync(LoginDto loginDto)
    {
        try
        {
            var response = await Program.HttpClient.PostAsJsonAsync("/api/auth/login", loginDto);
            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<AuthLoginResponse>(responseBody);
            await _localStorage.SetItemAsync("authToken", result?.Token);
            ((ApiAuthenticationStateProvider)_authenticationStateProvider).MarkUserAsAuthenticated(loginDto.Username);
            Program.HttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", result?.Token);
            return await response.Content.ReadFromJsonAsync<LoginResponseDto>()
                   ?? new LoginResponseDto
                   {
                       Success = false, Message = "Không thể đọc phản hồi từ máy chủ"
                   };
        }
        catch (Exception ex)
        {
            return new LoginResponseDto
            {
                Success = false, Message = $"Lỗi khi đăng nhập: {ex.Message}"
            };
        }
    }
    public async Task Logout()
    {
        await _localStorage.RemoveItemAsync("authToken");
        ((ApiAuthenticationStateProvider)_authenticationStateProvider).MarkUserAsLoggedOut();
        Program.HttpClient.DefaultRequestHeaders.Authorization = null;
    }
}
