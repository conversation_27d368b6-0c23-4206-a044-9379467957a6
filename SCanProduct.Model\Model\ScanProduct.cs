﻿using System.ComponentModel.DataAnnotations.Schema;
namespace SCanProduct.Model.Model;

[Table("SCanProducts")]
public class ScanProduct
{
    public int ProductId { get; set; }

    public string ItemCode { get; set; } = null!;

    public string UnitOfMeasure { get; set; } = null!;

    public int Length { get; set; }

    public int Width { get; set; }

    public int Height { get; set; }

    public int Weight { get; set; }

    public bool Block { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? CreateBy { get; set; }

    public virtual ICollection<Image> Images { get; set; } = new List<Image>();

    public virtual Item ItemCodeNavigation { get; set; } = null!;
}
