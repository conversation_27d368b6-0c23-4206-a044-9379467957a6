﻿@page "/"
@using SCanProduct.Model.Dto
@inherits ListProductBase

<AuthorizeView>
    <NotAuthorized>
        <RedirectToLogin/>
    </NotAuthorized>
    <Authorized>
        <div class="d-flex justify-content-between">
            <div class="">
                <h3><PERSON><PERSON> sách các sản phẩm đã đư<PERSON> quét</h3>
            </div>
            <div class="">
                <Button ButtonStyle="ButtonStyle.None" IsAsync="true" ButtonType="ButtonType.Button" @onclick="@(async () => await OnExportExcel())"
                        Color="Color.Success">
                    <i class="fa-solid fa-file-excel"></i>
                    Xuất Excel
                </Button>
            </div>
        </div>
        @if (IsLoading)
        {
            <p>Loading...</p>
        }
        else
        {
            <Table ShowLineNo
                   LineNoText="#"
                   IsFixedHeader="true"
                   IsFixedFooter="true"
                   ShowGotoNavigator="false"
                   IsPagination="true"
                   IsStriped="true"
                   SearchModel="@SearchModel"
                   ShowToolbar
                   ShowSearch
                   ShowEditButton="false"
                   ShowDeleteButton="false"
                   ShowAddButton="false"
                   ShowSearchButton="false"
                   ShowAdvancedSearch="false"
                   SearchPlaceholderText="Search item number"
                   ShowEmpty
                   PageItemsSource="@PageItemsSource"
                   ShowLoading="true"
                   ShowPageInfo="true"
                   OnQueryAsync="@OnQueryAsync"
                   ShowRefresh="true"
                   ShowFooter="true"
                   Height="700" TItem="DetailScanProductDto">
                <TableColumns Context="tableProduct">
                    <TableColumn @bind-Field="@tableProduct.ItemCode" Width="100"/>
                    <TableColumn @bind-Field="@tableProduct.ItemName" Width="430"/>
                    <TableColumn @bind-Field="@tableProduct.UnitOfMeasure" Width="100"/>
                    <TableColumn @bind-Field="@tableProduct.Length" Width="150"/>
                    <TableColumn @bind-Field="@tableProduct.Width" Width="150"/>
                    <TableColumn @bind-Field="@tableProduct.Height" Width="150"/>
                    <TableColumn @bind-Field="@tableProduct.CreateBy" Width="150"/>
                    <TableColumn @bind-Field="@tableProduct.CreateDate" FormatString="dd/MM/yyyy HH:mm"/>
                    <TableColumn @bind-Field="@tableProduct.ProductId" Text="Hình ảnh / Xóa">
                        <Template Context="value">
                            <Button ButtonStyle="ButtonStyle.None" class="me-3" ButtonType="ButtonType.Button"
                                    @onclick="@(() => OnClickToPreviewImg(value.Row.ProductId))" Size="Size.Small" Color="Color.Primary">
                                <i class="fa-solid fa-image  "></i>
                            </Button>
                            <Button ButtonStyle="ButtonStyle.None" ButtonType="ButtonType.Button" @onclick="@(() => OnClickToBlockProduct(value.Row.ProductId))"
                                    Size="Size.Small" Color="Color.Danger">
                                <i class="fa-solid fa-trash"></i>
                            </Button>
                        </Template>
                    </TableColumn>
                </TableColumns>
            </Table>
            <Modal @ref="PreviewImageModal" IsBackdrop="true">
                <ModalDialog FullScreenSize="FullScreenSize.ExtraLarge" ShowCloseButton="false" Title="Hình ảnh sản phẩm ">
                    <BodyTemplate>
                        <div class="row">
                            @if (ListImageToPreview.Any())
                            {
                                foreach (var o in ListImageToPreview)
                                {
                                    <div class="col-6">
                                        <img src="@o" class="border border-1 p-2 m-2" width="100%"/>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-center">
                                    <b class="py-4">
                                        Không có ảnh
                                    </b>
                                </div>
                            }
                        </div>
                    </BodyTemplate>
                </ModalDialog>
            </Modal>
            <Modal @ref="ConfirmBlockProductModal" IsBackdrop="true">
                <ModalDialog FullScreenSize="FullScreenSize.Small" OnSaveAsync="@(() => OnSaveAsync())" Size="Size.Medium" ShowCloseButton="false"
                             ShowSaveButton
                             SaveButtonText="Đồng ý" SaveButtonIcon="fa-solid fa-trash" Title="Xác nhận xóa?">
                    <BodyTemplate>
                        <Alert Icon="fa-solid fa-circle-xmark" Color="Color.Danger">
                            <b>Bạn có chắc chắn muốn xóa sản phầm này không?</b>
                            <div class="mb-2 ps-4">
                                Thông tin sản phẩm bạn muốn xóa:
                            </div>
                            <ul class="list-group">
                                <li class="list-group-item">Mã sản phẩm: <b>@CurrentProductInfo.ItemCode</b></li>
                                <li class="list-group-item">BarCode của NCC: <b>@CurrentProductInfo.BarCode</b></li>
                                <li class="list-group-item">Đơn vị: <b>@CurrentProductInfo.UnitOfMeasure</b></li>
                                <li class="list-group-item">Chiều dài: <b>@CurrentProductInfo.Length</b></li>
                                <li class="list-group-item">Chiều rộng: <b>@CurrentProductInfo.Width</b></li>
                                <li class="list-group-item">Chiều cao: <b>@CurrentProductInfo.Height</b></li>
                                <li class="list-group-item">Trọng lượng: <b>@CurrentProductInfo.Weight</b></li>
                                <li class="list-group-item">Ngày tạo: <b> @(CurrentProductInfo.CreateDate.ToString("dd/MM/yyyy HH:mm"))</b></li>
                                <li class="list-group-item">Người tạo: <b>@CurrentProductInfo.CreateBy</b></li>
                            </ul>
                        </Alert>
                    </BodyTemplate>
                </ModalDialog>
            </Modal>
        }
    </Authorized>
</AuthorizeView>

