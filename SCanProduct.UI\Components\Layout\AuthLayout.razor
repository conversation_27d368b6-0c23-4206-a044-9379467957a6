@inherits LayoutComponentBase

<AuthorizeView>
    <NotAuthorized>
        <Login />
    </NotAuthorized>
    <Authorized>
        <BootstrapBlazorRoot>
            <Layout SideWidth="0" TabDefaultUrl="/auth/change-password"
                    AdditionalAssemblies="new[] { GetType().Assembly }" class="">
                <Main>
                    <CascadingValue Value="this" IsFixed="true">
                        @Body
                    </CascadingValue>
                </Main>

                <NotFound>
                    <p>Sorry, there's nothing at this address.</p>
                </NotFound>
            </Layout>

        </BootstrapBlazorRoot>
    </Authorized>
</AuthorizeView>
