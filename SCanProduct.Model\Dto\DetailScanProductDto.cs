﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace SCanProduct.Model.Dto;

public class DetailScanProductDto
{
    public int ProductId { get; set; }
    [StringLength(100)]
    [DisplayName("Mã hàng")]
    public string ItemCode { get; set; } = null!;
    [StringLength(250)]
    public string ItemName { get; set; } = null!;
    [StringLength(100)]
    [DisplayName("Barcode của NCC")]
    public string BarCode { get; set; } = null!;
    [StringLength(100)]
    [DisplayName("Đơn vị")]
    public string UnitOfMeasure { get; set; } = null!;
    [DisplayName("Chiều dài(mm)")]
    public int Length { get; set; }
    [DisplayName("Chiều rộng(mm)")]
    public int Width { get; set; }
    [DisplayName("Chiều cao(mm)")]
    public int Height { get; set; }
    [DisplayName("Trọng lượng(mg)")]
    public int Weight { get; set; }
    [DisplayName("Ngày tạo")]
    [Column(TypeName = "datetime")]
    public DateTime CreateDate { get; set; }
    [DisplayName("Người tạo")][StringLength(50)]
    public string? CreateBy { get; set; }
    public bool Block { get; set; }
    public virtual ICollection<DetailImageDto> Images { get; set; } = new List<DetailImageDto>();
}
