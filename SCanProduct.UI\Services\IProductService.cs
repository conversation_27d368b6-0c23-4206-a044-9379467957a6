﻿using SCanProduct.Model.Common;
using SCanProduct.Model.Dto;
using SCanProduct.UI.Models;
namespace SCanProduct.UI.Services;

public interface IProductService
{
    public Task<bool> BlockProduct(int productId);
    public Task<string> CreateProduct(CreateProductModel model);
    public Task<Pagination<DetailScanProductDto>> GetListProducts(int pageNumber = 1, int pageSize = 10,
        string? itemNumber = null);
    public Task<List<DetailScanProductDto>> GetListProductsByItemNumber(string itemNumber = "");
    public Task<List<string>> GetUomByItemNo(string itemNo);
    public Task<string> UploadProductImage(MultipartFormDataContent content);
    public Task<object?> ExportAllProductToExcel();
    #region v2
    public Task<ApiResponse?> CreateProductV2(ScanProductV2Dto model);//in use
    public Task<PaginationV2<WarehouseProductDto>> GetListProductsV2(int pageNumber = 1, int pageSize = 10, string? itemNumber = null,
        string? itemName = null, string? locationCode = null);// in use
    #endregion v2
}
